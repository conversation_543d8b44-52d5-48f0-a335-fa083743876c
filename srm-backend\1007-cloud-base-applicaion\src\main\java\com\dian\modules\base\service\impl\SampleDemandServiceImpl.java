/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 * <p>
 * http://www.9dyun.cn
 * <p>
 * 版权所有，侵权必究！
 */
package com.dian.modules.base.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dian.client.base.BaseClient;
import com.dian.client.sys.SysClient;
import com.dian.common.utils.BeanConverter;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.dian.enums.WhetherEnum;
import com.dian.modules.base.dao.VendorDao;
import com.dian.modules.base.entity.*;
import com.dian.modules.base.service.SampleDemandVendorService;
import com.dian.modules.base.service.SampleService;
import com.dian.modules.base.vo.*;
import com.dian.modules.sys.vo.*;
import com.dian.modules.enums.base.CaseStatEnum;
import com.dian.modules.enums.base.DemandClassTypeEnum;
import com.dian.modules.enums.base.SampleEnums;
import com.dian.modules.plm.service.PlmApiService;
import com.dian.modules.sys.vo.SysUserVO;
import com.dian.vo.EmailMessageVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dian.common.exception.RRException;
import org.slf4j.Logger;
import com.dian.common.log.TraceLoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections.CollectionUtils;
import com.dian.common.utils.PageUtils;
import com.dian.common.utils.Query;
import com.dian.common.server.CommonService;
import com.dian.modules.base.dao.SampleDemandDao;

import com.dian.modules.base.service.SampleDemandService;
import com.dian.modules.base.service.SampleDemandItemService;

/**
 * 样品需求单服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-26 14:25:26
 */
@Service("sampleDemandService")
public class SampleDemandServiceImpl extends ServiceImpl<SampleDemandDao, SampleDemandEntity> implements SampleDemandService {

    @Autowired
    public CommonService commonService;
    @Autowired
    public SampleDemandItemService sampleDemandItemService;
    @Autowired
    public SampleDemandVendorService sampleDemandVendorService;
    @Autowired
    public SampleService sampleService;
    protected Logger logger = TraceLoggerFactory.getLogger(getClass());
    @Autowired
    private VendorDao vendorDao;
    @Autowired
    private SysClient sysClient;
    @Autowired
    private BaseClient baseClient;
    @Autowired
    private SampleDemandService sampleDemandService;
    @Autowired
    private PlmApiService plmApiService;

//    /**
//     * 样品需求单分页
//     *
//     * @param params
//     * @return
//     */
//    @Override
//    public PageUtils queryPage(Map<String, Object> params) {
//        // 默认查询显示未完成的数据
//        if (StrUtil.isEmptyIfStr(params.get("selectType"))) {
//            params.put("selectType", "1");
//        }
//        // 默认查询显示采购打样单据
//        if(StrUtil.isEmptyIfStr(params.get("demandClassType"))){
//            params.put("demandClassType","2");
//        }
//        IPage<SampleDemandEntity> page = this.page(new Query<SampleDemandEntity>().getPage(params), getQueryWrapper(params));
//        List<SampleDemandEntity> records;
//        if ("1".equals(params.get("selectType"))) { // 查询显示未完成的数据
//            records = page.getRecords();
//            List<SampleDemandEntity> unFinishedRecords = records.stream()
//                .filter(record -> !isCompleted(record))
//                .collect(Collectors.toList());
//            page.setRecords(unFinishedRecords);
//        } else if ("2".equals(params.get("selectType"))) { // 查询显示已完成数据
//            records = page.getRecords();
//            List<SampleDemandEntity> finishedRecords = records.stream()
//                .filter(record -> isCompleted(record))
//                .collect(Collectors.toList());
//            page.setRecords(finishedRecords);
//        }
//        return new PageUtils(page);
//    }
//
//    /**
//     * 判断送样需求单是否已完成
//     *
//     * @param record
//     * @return
//     */
//    private boolean isCompleted(SampleDemandEntity record) {
//        // 查询该送样需求单关联的所有送样通知单
//        List<SampleEntity> sampleEntityList = sampleService.list(
//            new LambdaQueryWrapper<SampleEntity>()
//                .eq(SampleEntity::getSourceId, record.getId())
//                .eq(SampleEntity::getIsValid, WhetherEnum.YES.getCode())
//                .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
//        );
//        if (CollectionUtil.isEmpty(sampleEntityList)) {
//            return false;
//        }
//        return sampleEntityList.stream()
//            .allMatch(sample -> SampleEnums.STAT7.getValue().equals(sample.getSampleStat()));
//    }

    /**
     * 样品需求单分页
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        // 默认查询显示未完成的数据
        if (StrUtil.isEmptyIfStr(params.get("selectType"))) {
            params.put("selectType", "1");
        }
        // 默认查询显示采购打样单据
        if(StrUtil.isEmptyIfStr(params.get("demandClassType"))){
            params.put("demandClassType","2");
        }
        if(Integer.parseInt(params.get("demandClassType").toString()) == 3){ // 全部打样类型送样需求单
            params.put("demandClassType",""); // 查询全部类型的打样单据，包括内部打样以及采购打样
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        // 查询数据
        List<HashMap<String, Object>> sampleDemandLineList = getBaseMapper().getSampleDemandLineList(page, params);
        page.setRecords(sampleDemandLineList);
        List<HashMap<String, Object>> records = page.getRecords();
        if ("1".equals(params.get("selectType"))) { // 查询显示未完成的数据
//            records = page.getRecords();
            List<HashMap<String, Object>> unFinishedRecords = records.stream()
                    .filter(record -> !isCompleted(record))
                    .collect(Collectors.toList());
            page.setRecords(unFinishedRecords);
        } else if ("2".equals(params.get("selectType"))) { // 查询显示已完成数据
//            records = page.getRecords();
            List<HashMap<String, Object>> finishedRecords = records.stream()
                    .filter(record -> isCompleted(record))
                    .collect(Collectors.toList());
            page.setRecords(finishedRecords);
        }
        logger.info("查询列表数据------------------>" + records);
//        List<SampleDemandVendorEntity> vendorList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            for (HashMap<String, Object> record : records) {
                List<SampleDemandVendorEntity> sampleDemandVendorEntityList = sampleDemandVendorService.query()
                        .eq("demand_id", record.get("id"))
                        .eq("goods_id",record.get("goodsId"))
                        .list();
//                vendorList.addAll(sampleDemandVendorEntityList);
                record.put("sampleDemandVendorEntityList", sampleDemandVendorEntityList);
            }
        }
        return new PageUtils(page);
    }

    /**
     * 判断送样需求单是否已完成
     *
     * @param record
     * @return
     */
    private boolean isCompleted(HashMap<String, Object> record) {
        // 查询该送样需求单关联的所有送样通知单
        List<SampleEntity> sampleEntityList = sampleService.list(
                new LambdaQueryWrapper<SampleEntity>()
                        .eq(SampleEntity::getSourceId, record.get("id"))
                        .eq(SampleEntity::getIsValid, WhetherEnum.YES.getCode())
                        .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
        );
        if (CollectionUtil.isEmpty(sampleEntityList)) {
            return false;
        }
        return sampleEntityList.stream()
                .allMatch(sample -> SampleEnums.STAT7.getValue().equals(sample.getSampleStat()));
    }

    /**
     * 样品需求单新增
     *
     * @param sampleDemandEntity
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public Long saveInfo(SampleDemandEntity sampleDemandEntity) {
        //设置编码，等基础默然初始化数据设置
        //sampleDemandEntity.setOrderDate(new Date());
        //数据完整性校验
        this.paramsCheck(sampleDemandEntity, AddGroup.class);
        //保存主表
        SampleDemandEntity sampleDemand = new SampleDemandEntity();
        BeanUtils.copyProperties(sampleDemandEntity,sampleDemand);
        sampleDemand.setId(null);
        sampleDemand.setTenantPId(0L);
        sampleDemand.setTenantId(commonService.getTenantId());
        sampleDemand.setDemandNo(sysClient.getBillNo("base_sample_demand"));
        sampleDemand.setOrgId(28905L);
        sampleDemand.setOrgCode("101");
        sampleDemand.setOrgName("佛山市顺德区冠宇达电源有限公司");
        sampleDemand.setIsNeedUpFile(sampleDemandEntity.getIsNeedUpFile());
        sampleDemand.setApplicant(sampleDemandEntity.getApplicant());
        sampleDemand.setDemandClassType(sampleDemandEntity.getDemandClassType());
        sampleDemand.setApplyDate(new Date());
        if (!StrUtil.isEmptyIfStr(sampleDemandEntity.getDemandFileUrl())){
            sampleDemand.setDemandFileUrl(sampleDemandEntity.getDemandFileUrl());
        }
        this.save(sampleDemand);
        //保存从表（物料明细）
        if (CollectionUtils.isNotEmpty(sampleDemand.getSampleDemandItemEntityList())) {
            List<SampleDemandItemEntity> sampleDemandItemEntityList = sampleDemand.getSampleDemandItemEntityList();
            for (SampleDemandItemEntity item : sampleDemandItemEntityList) {
                item.setId(null);
                item.setTenantPId(0L);
                item.setTenantId(commonService.getTenantId());
                item.setDemandId(sampleDemand.getId());
                item.setCaseStat(DemandClassTypeEnum.PURCHASE_DEMAND.getValue().equals(sampleDemand.getDemandClassType()) ? CaseStatEnum.STAT1.getValue() : CaseStatEnum.STAT2.getValue()); // 采购打样--待分配   内部打样--已分配
                item.setIsValid(WhetherEnum.YES.getCode());
                item.setDeleteFlag(WhetherEnum.NO.getCode());
                sampleDemandItemService.save(item);
            }
        }
        if(DemandClassTypeEnum.PURCHASE_DEMAND.getValue().equals(sampleDemand.getDemandClassType())){ // 内部打样没有供应商信息
            // 采购打样-----保存从表（供应商明细）
            List<SampleDemandVendorEntity> sampleDemandVendorEntityList = sampleDemand.getSampleDemandVendorEntityList();
            for(SampleDemandVendorEntity vendor : sampleDemandVendorEntityList){
                vendor.setId(null);
                vendor.setTenantPId(0L);
                vendor.setTenantId(commonService.getTenantId());
                vendor.setDemandId(sampleDemand.getId());
                vendor.setCaseStat(CaseStatEnum.STAT1.getValue()); // 待分配
                vendor.setIsValid(WhetherEnum.YES.getCode());
                vendor.setDeleteFlag(WhetherEnum.NO.getCode());
            }
            sampleDemandVendorService.updateInfo(sampleDemand);
        }
        logger.info("新建送样需求单保存数据：{}", JSONObject.toJSONString(sampleDemand));
        // 内部打样时自动生成送样通知单
        if (DemandClassTypeEnum.RESEARCH_DEMAND.getValue().equals(sampleDemand.getDemandClassType())) {
            sampleService.batchAutoInsertSampleByPlm(sampleDemand);
        }
        return sampleDemand.getId(); // 主键回填
    }

    /**
     * 样品需求单更新
     *
     * @param sampleDemandEntity
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public Long updateInfo(SampleDemandEntity sampleDemandEntity) {

        //修改状态校验
        this.updateCheck(sampleDemandEntity.getId());

        //主表数据完整性校验
        this.paramsCheck(sampleDemandEntity, UpdateGroup.class);

        //更新主表
        this.updateById(sampleDemandEntity);

        //更新从表
        sampleDemandItemService.updateInfo(sampleDemandEntity);

        sampleDemandVendorService.updateInfo(sampleDemandEntity);
        return sampleDemandEntity.getId();
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean saveInfoByPlm(SampleDemandVo sampleDemandVo) {
        SampleDemandEntity demand = getOne(new LambdaQueryWrapper<SampleDemandEntity>()
                .eq(SampleDemandEntity::getTenantId, commonService.getTenantId())
                .eq(SampleDemandEntity::getDemandNo, sampleDemandVo.getDemandNo())
                .eq(SampleDemandEntity::getDemandClassType, sampleDemandVo.getDemandClassType())
        );
        SampleDemandEntity saveData = BeanConverter.convert(sampleDemandVo, SampleDemandEntity.class);
        if (ObjectUtil.isEmpty(demand)) {
            saveData.setTenantId(commonService.getTenantId());
            saveData.setTenantPId(0L);
//            saveData.setDemandClassType(DemandClassTypeEnum.RESEARCH_DEMAND.getValue());
            logger.info("保存数据：{}", JSONObject.toJSONString(saveData));
            this.save(saveData);
            sampleDemandItemService.batchSaveByPlm(saveData, sampleDemandVo);
            // 内部打样时自动生成送样通知单
            if (DemandClassTypeEnum.RESEARCH_DEMAND.getValue().equals(saveData.getDemandClassType())) {
                sampleService.batchAutoInsertSampleByPlm(saveData);
            }
//            sampleService.batchInsertSampleByPlm(saveData);
        } else {
            saveData.setTenantId(commonService.getTenantId());
            saveData.setTenantPId(0L);
//            saveData.setDemandClassType(DemandClassTypeEnum.RESEARCH_DEMAND.getValue());
            saveData.setId(demand.getId());
            sampleDemandItemService.batchSaveByPlm(saveData, sampleDemandVo);
            this.updateById(saveData);
            // 内部打样时自动生成送样通知单
            if (DemandClassTypeEnum.RESEARCH_DEMAND.getValue().equals(saveData.getDemandClassType())){
                sampleService.batchAutoInsertSampleByPlm(saveData);
            }
//            sampleService.batchInsertSampleByPlm(saveData);
        }

        return true;
    }

    /**
     * 样品需求单删除
     *
     * @param id
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean deleteInfo(Long id) {

        //删除状态校验
        this.deleteCheck(id);

        //更新从表
        sampleDemandItemService.deleteInfo(id);

        //更新主表
        this.remove(new QueryWrapper<SampleDemandEntity>().eq("id", id));

        return true;
    }

    /**
     * 样品需求单详情
     *
     * @param id
     * @return
     */
    @Override
    public SampleDemandEntity getInfo(Long id) {
        SampleDemandEntity sampleDemandEntity = getById(id);
        List<SampleDemandItemEntity> lineList = sampleDemandItemService.queryLineList(id);
        sampleDemandEntity.setSampleDemandItemEntityList(lineList);
        sampleDemandEntity.setSampleDemandVendorEntityList(sampleDemandVendorService.queryList(id));
        return sampleDemandEntity;
    }

    /**
     * 样品需求单审核
     *
     * @param id
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean checkInfo(Long id) {
        SampleDemandEntity sampleDemandEntity = this.getById(id);
        checkCheck(sampleDemandEntity);
        //sampleDemandEntity.setOrderState(OrderHead_OrderStateEnum.AUDITED.getValue());
        //sampleDemandEntity.setOrderDate(new Date());
        ///sampleDemandEntity.setCheckUserId(commonService.getUserId());
        //sampleDemandEntity.setCheckUserName(commonService.getUserName());
        return this.updateById(sampleDemandEntity);
    }

    /**
     * 分配下发生成供应商送样单
     *
     * @param id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean issuedSample(Long id) {
        SampleDemandEntity info = this.getInfo(id);
        if (ObjectUtil.isEmpty(info)) {
            throw new RuntimeException("未找到该送样需求单");
        }
        if (CollectionUtil.isEmpty(info.getSampleDemandItemEntityList())){
            throw new RRException("未查询到送样需求单明细");
        }
        if (CollectionUtil.isEmpty(info.getSampleDemandVendorEntityList())){
            throw new RRException("未查询到送样需求单供应商明细");
        }
        List<SampleDemandItemEntity> sampleDemandItemEntityList = sampleDemandItemService.queryLineList(id);
        List<SampleDemandVendorEntity> sampleDemandVendorEntityList = info.getSampleDemandVendorEntityList();
        // 过滤掉已制单的明细行，不参与生成送样通知单
//        List<SampleDemandItemEntity> orderedItems = sampleDemandItemEntityList.stream()
//                .filter(item -> CaseStatEnum.STAT1.getValue().equals(item.getCaseStat()))
//                .collect(Collectors.toList());
//        List<SampleDemandVendorEntity> orderedVendors = sampleDemandVendorEntityList.stream()
//                .filter(vendor -> CaseStatEnum.STAT1.getValue().equals(vendor.getCaseStat()))
//                .collect(Collectors.toList());
//        if (!orderedItems.isEmpty() && !orderedVendors.isEmpty()) {
//            SampleDemandEntity filteredInfo = new SampleDemandEntity();
//            BeanUtils.copyProperties(info, filteredInfo);
//            filteredInfo.setSampleDemandItemEntityList(orderedItems);
//            filteredInfo.setSampleDemandVendorEntityList(orderedVendors);
//            sampleService.issuedSample(filteredInfo);
//        }
        sampleService.issuedSample(info);
        // 更新物料明细行和供应商明细行的状态
        sampleDemandVendorEntityList.forEach(sampleDemandVendorEntity -> {
            SampleDemandItemEntity sampleDemandItemEntity = sampleDemandItemEntityList.stream()
                    .filter(demandItem -> demandItem.getGoodsId().equals(sampleDemandVendorEntity.getGoodsId())
                            && WhetherEnum.YES.getCode().equals(demandItem.getIsValid())
                            && WhetherEnum.NO.getCode().equals(demandItem.getDeleteFlag())
                            && CaseStatEnum.STAT1.getValue().equals(sampleDemandVendorEntity.getCaseStat())) // 待分配
                    .findFirst().orElse(null);
            if(sampleDemandItemEntity != null) {
                if(sampleDemandVendorEntityList.stream().noneMatch(vendor -> CaseStatEnum.STAT3.getValue().equals(vendor.getCaseStat()) && vendor.getGoodsId().equals(sampleDemandItemEntity.getGoodsId()))) {
                    sampleDemandItemEntity.setCaseStat(CaseStatEnum.STAT2.getValue()); // 已分配
                    sampleDemandItemService.updateById(sampleDemandItemEntity);
                }
                sampleDemandVendorEntity.setCaseStat(CaseStatEnum.STAT2.getValue()); // 已分配
                sampleDemandVendorEntity.setAssignDate(new Date()); // 分配时间
                sampleDemandVendorEntity.setAssigner(commonService.getUserName()); // 分配人
                sampleDemandVendorService.updateById(sampleDemandVendorEntity);
            }
        });
        return true;
    }

    /**
     * 批量生成送样通知单
     * @param ids
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public Boolean batchIssuedSample(Long[] ids) {
        for (Long id : ids) {
            this.issuedSample(id);
        }
        return true;
    }

    /**
     * 样品需求单物料明细行退回给PLM
     *
     * @param params
     * @return
     */
    @Override
    public Long returnSample(Map<String, Object> params) {
        String idsStr = params.get("ids").toString()
                .replace("[", "")
                .replace("]", "");
        Long[] ids = Arrays.stream(idsStr.split(","))
                .map(String::trim)
                .map(Long::parseLong)
                .toArray(Long[]::new);
        SampleDemandItemEntity firstSampleDemandItem = sampleDemandItemService.getById(ids[0]);
        SampleDemandEntity sampleDemand = sampleDemandService.getInfo(firstSampleDemandItem.getDemandId());
        if (sampleDemand == null) {
            throw new RRException("送样需求单不存在");
        }
        List<SampleDemandItemEntity> returnedItems = new ArrayList<>();
        for(Long id : ids){
            // 退回送样，更新送样需求单物料明细需求状态为已退回
            SampleDemandItemEntity sampleDemandItemEntity = sampleDemandItemService.getById(id);

            if(ObjectUtil.isEmpty(sampleDemandItemEntity)){
                throw new RRException("未查询到该物料明细数据");
            }
            sampleDemandItemEntity.setCaseStat(CaseStatEnum.STAT4.getValue()); // 已退回
            sampleDemandItemEntity.setReturnCause(params.get("returnCause").toString()); // 退回原因
            sampleDemandItemService.updateById(sampleDemandItemEntity);
            returnedItems.add(sampleDemandItemEntity);
        }
        List<PlmSampleVO> plmSampleVOList = new ArrayList<>();
        for (SampleDemandItemEntity item : returnedItems) {
            if (item.getSourceItemId() == null) {
                throw new RRException("送样需求单明细的来源明细ID为空");
            }
            PlmSampleVO plmSampleVO = new PlmSampleVO();
            plmSampleVO.setKeyid(item.getId().toString());
            plmSampleVO.setSourceitemid(item.getSourceItemId().toString());
            plmSampleVO.setSharestatus("已退回");
            plmSampleVO.setSupplier_abbr("");
            plmSampleVO.setSupplier_code("");
            plmSampleVO.setOrder_no(sampleDemand.getDemandNo());
            plmSampleVO.setOrder_status("已退回");
            plmSampleVO.setOrder_quantity(ObjectUtil.isEmpty(item.getDemandQty()) ? "" : item.getDemandQty().toString());
            plmSampleVO.setDelivered_quantity("");
            plmSampleVO.setDelivery_date("");
            plmSampleVO.setReply_quantity("");
            plmSampleVO.setReply_delivery_date("");
            plmSampleVO.setReceipt_status("已退回");
            plmSampleVO.setReceipt_date(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            plmSampleVO.setReceiver(commonService.getUserName());
            plmSampleVO.setStatus("已退回");
            plmSampleVO.setConfirm_date(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            plmSampleVO.setConfirmer(commonService.getUserName());
            plmSampleVO.setUrl("");
            plmSampleVOList.add(plmSampleVO);
        }
        if (!plmSampleVOList.isEmpty()) {
            JSONObject jsonData = new JSONObject();
            jsonData.put("DATAINFOS", plmSampleVOList);
            logger.info("SRM推送退回送样需求单至PLM接口传参信息=" + jsonData.toJSONString());
            plmApiService.sendPlmV2("/api/intf/service/gyd_cpcsrm_supplyAndOrder", jsonData.toJSONString(), "退回送样需求同步至PLM", sampleDemand.getTenantId());
        }
        return sampleDemand.getId();
    }


    /**
     * 批量更换采购员
     * @param params
     * @return
     */
    @Override
    public Boolean batchUpdateBuyer(Map<String, Object> params) {
        logger.info("更换采购员传入参数------------>{}",params );
        String idsStr = params.get("ids").toString()
                .replace("[", "")
                .replace("]", "");
        Long[] ids = Arrays.stream(idsStr.split(","))
                .map(String::trim)
                .map(Long::parseLong)
                .toArray(Long[]::new);
        for (Long id : ids) {
            // 更新送样需求单物料明细采购员信息
            SampleDemandItemEntity sampleDemandItemEntity = sampleDemandItemService.getById(id);
            if(ObjectUtil.isEmpty(sampleDemandItemEntity)){
                throw new RRException("未查询到该物料明细数据");
            }
            sampleDemandItemEntity.setPurCode(params.get("purCode").toString()); // 采购员编码
            sampleDemandItemEntity.setPurName(params.get("purName").toString()); // 采购员名称
            sampleDemandItemEntity.setPurId(Long.parseLong(params.get("purId").toString())); // 采购员ID
            sampleDemandItemService.updateById(sampleDemandItemEntity);
        }

        return true;
    }

    /**
     * 批量删除物料明细
     * @param ids
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean deleteSampleDemandItems(Long[] ids) {
        for (Long id : ids) {
            SampleDemandItemEntity item = sampleDemandItemService.getById(id);
            if (ObjectUtil.isEmpty(item)) {
                throw new RRException("未查询到该物料明细数据");
            }
            if (!CaseStatEnum.STAT1.getValue().equals(item.getCaseStat())) {
                throw new RRException("只能删除待分配状态的物料明细");
            }
            sampleDemandItemService.removeById(id);
            // 删除对应的供应商明细
            sampleDemandVendorService.remove(
                new LambdaQueryWrapper<SampleDemandVendorEntity>()
                    .eq(SampleDemandVendorEntity::getDemandId, item.getDemandId())
                    .eq(SampleDemandVendorEntity::getGoodsId, item.getGoodsId())
            );
        }
        return true;
    }

    /**
     * 批量删除供应商明细
     * @param ids
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean deleteSampleDemandVendors(Long[] ids) {
        for (Long id : ids) {
            SampleDemandVendorEntity vendor = sampleDemandVendorService.getById(id);
            if (ObjectUtil.isEmpty(vendor)) {
                throw new RRException("未查询到该供应商明细数据");
            }
            if (!CaseStatEnum.STAT1.getValue().equals(vendor.getCaseStat())) {
                throw new RRException("只能删除待分配状态的供应商明细");
            }
            sampleDemandVendorService.removeById(id);
        }
        return true;
    }


    /**
     * PLM同步送样需求单结案数据
     * @param sampleDemandClosedItemVo
     * @return
     */
    @Override
    public boolean syncSampleDemandClosedByPlm(SampleDemandClosedItemVo sampleDemandClosedItemVo) {

        SampleDemandEntity sampleDemand = sampleDemandService.getOne(new LambdaQueryWrapper<SampleDemandEntity>().eq(SampleDemandEntity::getDemandNo, sampleDemandClosedItemVo.getDemandNo()));
        if (ObjectUtil.isEmpty(sampleDemand)) {
            throw new RRException("未查询到该需求单数据");
        }
        SampleDemandItemEntity sampleDemandItem = sampleDemandItemService.getOne(new LambdaQueryWrapper<SampleDemandItemEntity>()
                .eq(SampleDemandItemEntity::getDemandId, sampleDemand.getId())
                .eq(SampleDemandItemEntity::getSourceItemId, sampleDemandClosedItemVo.getSourceItemId())
                .eq(SampleDemandItemEntity::getGoodsErpCode, sampleDemandClosedItemVo.getGoodsErpCode()));
        if(ObjectUtil.isEmpty(sampleDemandItem)){
            throw new RRException("未查询到该物料需求明细数据");
        }
        if (CaseStatEnum.STAT5.getValue().equals(sampleDemandItem.getCaseStat())) {
            throw new RRException("该物料需求单已结案，请勿重复结案");
        }
        // 更新结案状态和结案时间
        if(sampleDemandClosedItemVo.getClosedStatus() == 1){
            sampleDemandItem.setCaseStat(CaseStatEnum.STAT5.getValue()); // 已结案
        }else{
            throw new RRException("结案状态异常");
        }
        sampleDemandItem.setCaseDate(sampleDemandClosedItemVo.getClosedDate() == null ? new Date() : sampleDemandClosedItemVo.getClosedDate()); // 结案日期
        sampleDemandItemService.updateById(sampleDemandItem);
        // PLM确认结案送样需求单以邮箱消息和站内业务消息的方式通知采购方
        this.sendClosedMsgToPur(sampleDemandItem,"PLM");
        return true;
    }

    /**
     * 确认结案送样需求单发送信息通知采购员
     * @param sampleDemandItem
     */
    private void sendClosedMsgToPur(SampleDemandItemEntity sampleDemandItem,String operator){
        try {
            SampleDemandEntity sampleDemand = sampleDemandService.getInfo(sampleDemandItem.getDemandId());
            if (ObjectUtil.isNotEmpty(sampleDemandItem.getPurId())){
                List<SysUserVO> sysUserVOS = sysClient.queryUserByIds(Collections.singletonList(sampleDemandItem.getPurId()));
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId", sampleDemandItem.getTenantId());
                mailJson.put("userId", sampleDemandItem.getPurId());
                mailJson.put("content",String.format("%s确认结案了物料编码为%s的送样需求单%s",operator,sampleDemandItem.getGoodsErpCode(),sampleDemand.getDemandNo()));
                mailJson.put("menuTitle","送样需求单详情");//菜单标题
                mailJson.put("url","base/sampleDemand/tenant?id="+sampleDemand.getId());//菜单地址
                sysClient.sendMail(mailJson);
                // 采购员邮箱地址不为空
                if (CollectionUtil.isNotEmpty(sysUserVOS) && !StrUtil.isEmptyIfStr(sysUserVOS.get(0).getUserEmail())){
                    EmailMessageVo emailMessageVo = new EmailMessageVo();
                    emailMessageVo.setTenantId(sampleDemandItem.getTenantId());
                    emailMessageVo.setTitle("您有一封新的邮件信息");
                    String content = sampleDemandItem.getPurName()+",您好：\n" +
                            "<br>\n" +
                            "<br>("+operator+")确认结案了物料编码("+sampleDemandItem.getGoodsErpCode()+")的送样需求单("+sampleDemand.getDemandNo()+")，请知悉！ \n" +
                            "<br>\n" +
                            "<br>请您及时查阅、处理！ 如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n" +
                            "<br>\n";
                    emailMessageVo.setContent(content);
                    emailMessageVo.setEmail(sysUserVOS.get(0).getUserEmail());
                    baseClient.customSendEmail(emailMessageVo);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 批量本地结案
     * @param ids
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean localClosedItems(Long[] ids) {
        for (Long id : ids) {
            SampleDemandItemEntity item = sampleDemandItemService.getById(id);
            if (ObjectUtil.isEmpty(item)) {
                throw new RRException("未查询到该物料需求明细数据");
            }
            if (!StrUtil.isEmptyIfStr(item.getSourceItemId())) {
                throw new RRException("存在已关联PLM的物料，不允许本地结案");
            }
            item.setCaseStat(CaseStatEnum.STAT5.getValue()); // 已结案
            item.setCaseDate(new Date()); // 结案时间
            sampleDemandItemService.updateById(item);
            // 本地确认结案送样需求单以邮箱消息和站内业务消息的方式通知采购方
            this.sendClosedMsgToPur(item,commonService.getUserName());
        }
        return true;
    }

    /**
     * 样品需求单当前页or全部导出
     *
     * @param params
     * @return
     */
    @Override
    public List<SampleDemandExportVO> exportList(Map<String, Object> params) {
        List<SampleDemandEntity> list;
        if ("0".equals(params.get("exprotType"))) {
            list = this.page(new Query<SampleDemandEntity>().getPage(params), getQueryWrapper(params)).getRecords();
        } else {
            list = this.list(getQueryWrapper(params));
        }
        List<SampleDemandExportVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, SampleDemandExportVO.class);
            // 封装明细
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (SampleDemandExportVO sampleDemandEntity : resultList) {
                    List<SampleDemandItemEntity> sampleDemandItemEntityList = sampleDemandItemService.queryLineList(sampleDemandEntity.getId());
                    sampleDemandEntity.setSampleDemandItemExportVOList(BeanConverter.convertList(sampleDemandItemEntityList, SampleDemandItemExportVO.class));
                }
            }
        }
        return resultList;
    }




    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        SampleDemandEntity sampleDemandEntity = this.getById(id);
        //if(sampleDemandEntity.getOrderState().equals(SampleDemand_OrderStateEnum.AUDITED.getValue())){
        //     throw new RRException(String.format("[%s] 当前XXX非未审核状态,禁止修改",sampleDemandEntity.getProbNo()));
        //}
    }

    /**
     * 审核状态校验
     *
     * @param
     */
    private void checkCheck(SampleDemandEntity sampleDemandEntity) {

        //if(sampleDemandEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("[%s] 当前XXX非未审核状态,禁止审核",probHeadEntity.getProbNo()));
        //}
    }

    /**
     * 删除状态校验
     *
     * @param
     */
    private void deleteCheck(Long id) {
        SampleDemandEntity sampleDemandEntity = this.getById(id);
        //if(!sampleDemandEntity.getOrderState().equals(SampleDemand_OrderStateEnum.WAITCHECK.getValue())){
        //    throw new RRException(String.format("[%s] 当前XXX非未审核状态,禁止删除",SampleDemandEntity.getProbNo()));
        //}
    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(SampleDemandEntity record, Class<?> cls) {
        //校验主表实体类
        ValidatorUtils.validateEntity(record, cls);
        //校验明细表实体类
        //明细非空
        if (CollectionUtils.isEmpty(record.getSampleDemandItemEntityList())) {
            throw new RRException("样品需求单明细数据不能为空");
        }
    }

    /**
     * 获取查询条件
     *
     * @param
     */
    private LambdaQueryWrapper<SampleDemandEntity> getQueryWrapper(Map<String, Object> params) {
        LambdaQueryWrapper<SampleDemandEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SampleDemandEntity::getDeleteFlag, WhetherEnum.NO.getValue());
        queryWrapper.like(!StrUtil.isEmptyIfStr(params.get("demandNo")), SampleDemandEntity::getDemandNo, params.get("demandNo").toString().trim());
        queryWrapper.eq(!StrUtil.isEmptyIfStr(params.get("demandType")), SampleDemandEntity::getDemandType, params.get("demandType"));
        queryWrapper.eq(!StrUtil.isEmptyIfStr(params.get("isNeedUpFile")), SampleDemandEntity::getIsNeedUpFile, params.get("isNeedUpFile"));
        queryWrapper.like(!StrUtil.isEmptyIfStr(params.get("applicant")), SampleDemandEntity::getApplicant, params.get("applicant").toString().trim());
        queryWrapper.orderByDesc(SampleDemandEntity::getId);
        // 默认查询显示采购打样单据
        if(!StrUtil.isEmptyIfStr(params.get("demandClassType"))){
            queryWrapper.eq(SampleDemandEntity::getDemandClassType,params.get("demandClassType"));
        }
        if(!StrUtil.isEmptyIfStr(params.get("pur"))){
            queryWrapper.like(SampleDemandEntity::getPurCode,params.get("pur")).or().like(SampleDemandEntity::getPurName,params.get("pur"));
        }
        return queryWrapper;
    }
}
