<template>
  <div class="DIAN-common-layout">
    <div class="DIAN-common-layout-center">

      <!-- 搜索框 -->
      <el-row class="DIAN-common-search-box" :gutter="16">
        <el-form @submit.native.prevent>
          <el-col :span="6">
            <el-form-item>
              <el-date-picker
                v-model="queryParam.orderDate"
                type="daterange"
                placeholder="请输入订单日期"
                range-separator="至"
                start-placeholder="（订单）开始日期"
                end-placeholder="（订单）结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.purNo" placeholder="请输入订单号" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.vendor" placeholder="请输入供应商编码/名称" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-input v-model.trim="queryParam.goods" placeholder="请输入物料编码/名称/规格" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <template v-if="showAll">
            <el-col :span="4">
              <el-form-item>
                <el-input v-model.trim="queryParam.purName" placeholder="请输入采购员名称" @keyup.enter.native="search()" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-select v-model="queryParam.selectType" placeholder="请选择完成状态" clearable>
                  <el-option :key="item.key" :label="item.value" :value="item.key"
                            v-for="item in selectTypeOptions"/>
                </el-select>
              </el-form-item>
            </el-col>
           <!-- <el-col :span="4">
              <el-form-item>
                <el-select v-model="queryParam.returnMark" placeholder="是否退货订单" clearable>
                  <el-option
                    :key="item.key"
                    :label="item.value"
                    :value="parseInt(item.key)"
                    v-for="item in validStat"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-select v-model="queryParam.freeMark" placeholder="是否免费订单" clearable>
                  <el-option
                    :key="item.key"
                    :label="item.value"
                    :value="parseInt(item.key)"
                    v-for="item in validStat"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
          </template>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="search()" @keyup.enter.native="search()">{{$t('common.search')}}</el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()">{{$t('common.reset')}}</el-button>
              <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll"> 展开 </el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else> 收起 </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <!-- body -->
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <!-- 表头工具栏 -->
        <div class="DIAN-common-head">
          <div>

            <template>
              <el-button type="primary" @click="exportHandle()" icon="el-icon-top" v-has-per="'order:pur:progressExport'">{{ $t('common.exportBtn') }}</el-button>
            </template>
          </div>
          <div class="DIAN-common-head-right">
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false" @click="search()" />
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>

        <!-- 表格 -->
        <d-table ref="listTable" v-loading="listLoading" :data="list" hasC show-summary>
          <el-table-column prop="deptName" label="采购组织" align="center" show-overflow-tooltip width="120"/>
          <el-table-column prop="purNo" label="订单号/序号" align="center" width="150">
            <template slot-scope="scope">
              <span>{{scope.row.purNo + '/' + scope.row.seq}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="purName" label="采购员" align="center" show-overflow-tooltip width="90"/>
          <el-table-column prop="orderType" label="订单类型" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{scope.row.orderType | commonEnumsTurn("common.JinDieOrderTypeEnum")}}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="mrpRegion" label="MRP区域" align="center" show-overflow-tooltip width="100"/> -->
          <el-table-column prop="vendorCode" label="供应商编码" align="center" width="100"/>
          <el-table-column prop="vendorName" label="供应商名称" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="goodsErpCode" label="物料编码" align="center" show-overflow-tooltip width="110"/>
          <el-table-column prop="goodsName" label="物料名称" align="center" show-overflow-tooltip width="120"/>
          <el-table-column prop="goodsModel" label="规格型号" align="center" show-overflow-tooltip width="130"/>
          <el-table-column prop="deliveryDate" label="交货日期" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.deliveryDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="itemStat" label="产品状态" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.itemStat | commonEnumsTurn("order.PurlineStatEnum") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="freeMark" label="是否免费" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.freeMark | commonEnumsTurn("comm.ValidEnum") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="returnMark" label="是否退货" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.returnMark | commonEnumsTurn("comm.ValidEnum") }}</span>
            </template>
          </el-table-column> -->
<!--          <el-table-column prop="auxUomName" label="单位" align="center" show-overflow-tooltip width="50"/>-->
          <el-table-column prop="orderNum" label="订单数量" align="center" show-overflow-tooltip width="80"/>
          <!-- <el-table-column prop="matchedPlanNum" label="已匹配计划量" align="center" show-overflow-tooltip width="100"/> -->
          <!-- <el-table-column prop="canMatchPlanNum" label="可匹配计划量" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.returnMark != 1">{{scope.row.canMatchPlanNum}}</span>
              <span v-else>0</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="makeNum" label="已制单量" align="center" show-overflow-tooltip width="100">
          </el-table-column>
          <el-table-column prop="canMakeNum" label="未制单量" align="center" show-overflow-tooltip width="100">
          </el-table-column>
          <el-table-column prop="fixNum" label="已送货量" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="waitNum" label="待送货量" align="center" show-overflow-tooltip width="100"/>
          <!-- <el-table-column prop="unCompetentNum" label="不合格数" align="center" show-overflow-tooltip width="100"/> -->
          <el-table-column prop="refundNum" label="暂退补料数量" width="100"/>
          <el-table-column prop="refDedNum" label="暂退扣款数量" width="100"/>
          <el-table-column prop="erpMasterNum" label="入库数量" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="erpRejectNum" label="退货补料数量" width="100"/>
          <el-table-column prop="retDedNum" label="退货扣款数量" width="100"/>
          <el-table-column prop="shippingAddress" label="交货地址" align="center" show-overflow-tooltip/>
          <el-table-column prop="gstPrice" label="含税单价" align="center" show-overflow-tooltip width="100" v-if="$dian.hasPerBtnP('order:pur:lookPrice')"/>
          <el-table-column prop="gstAmount" label="含税金额" align="center" show-overflow-tooltip width="100" v-if="$dian.hasPerBtnP('order:pur:lookPrice')"/>
          <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip/>
          <el-table-column label="操作" width="80" fixed="right" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="addEditOrderHandle(scope.row.id)" v-has-per="'order:pur:info'"> {{ $t('common.lookBtn')}} </el-button>
            </template>
          </el-table-column>
        </d-table>
        <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initData"/>
      </div>

      <!-- FORM表单 -->
      <Form ref="form" v-show="formVisible" @callRefreshList="closeForm"></Form>
      <!-- 下载/导出 组件 -->
      <d-export ref="export" title="订单执行进度表导出" :exports="downExports"></d-export>
      <!-- 导入模板 -->
      <d-import ref="upload" @callData="search()" />


    </div>
  </div>
</template>

<script>

import {dFlowMixin} from "@dian/dian-ui-vue";
import Form from '@/views/order/produce/tenant/Form';
import store from "@/store";
import {getTenantSaleOrderProgressList} from "@/api/order/sale";
import dian from "@/utils/dian";

export default {
  //加载底层公有组件
  mixins: [dFlowMixin],
  name: "order-progress-tenant",
  components: {
    Form
  },
  data() {
    return {
      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型
      orderLineStat: store.getters.commonEnums['order.PurlineStatEnum'], // 订单明细单据状态
      validStat: store.getters.commonEnums['comm.ValidEnum'], // 订单明细单据状态
      selectTypeOptions: [ // 查询类型（完成状态）
        {
          key: '1',
          value: '未完成'
        },
        {
          key: '2',
          value: '已完成'
        },
        {
          key: '3',
          value: '全部'
        }
      ],
      queryParam: {
        page: 1,
        limit: 20,
        orderNo : '', // 订单号
        vendor : '', // 供应商编码|名称
        goods : '',  // 物料编码|名称|描述|图号
        orderDate : '',  // 订单日期
        startDate : '',  // 订单日期 - 开始日期
        endDate : '',  // 订单日期 - 结束日期
        orderType : '', // 订单类型
        itemStat: '', // 单据状态
        deptId:'',//组织机构id
        isTenant : 'ture', // 是否是采购方进入
        isDESC : 'ture', // 是否倒叙排序
        purName:'',//采购员名称
        returnMark: null, // 是否退货
        freeMark: null, // 是否免费
        selectType: '3', // 默认查询显示全部的数据
      },
      showAll: false,
      formVisible: false,
      listLoading: false,
      btnLoading: false,
      downExports: false,  // true-下载模板，false-导出
      list: [],
      total: 0
    }
  },
  created() {
    this.initData();
  },
  watch: {
    $route: {
      handler: function handler(to, from) {
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      this.listLoading = true;
      if (this.queryParam.orderDate.length !== 0) {
        const startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');
        const endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');
        this.queryParam.startDate = startDate;
        this.queryParam.endDate = endDate;
      } else {
        this.queryParam.startDate = '';
        this.queryParam.endDate = '';
      }
      let subCompanyInfoData = dian.storageGet('subCompanyInfo');
      if (subCompanyInfoData){
        this.queryParam.deptId = subCompanyInfoData.id;
      }
      const date = this.queryParam.orderDate;
      this.queryParam.orderDate = '';
      getTenantSaleOrderProgressList(this.queryParam).then(res => {
        this.total = res.data.totalCount;
        this.list = res.data.list;
        this.queryParam.orderDate = date;
        this.listLoading = false;
      }).catch(() => {
        this.queryParam.orderDate = date;
        this.listLoading = false;
      })
    },
    // 新增|编辑 项目报备
    addEditOrderHandle(id) {
      this.formVisible = true;
      this.$nextTick(() => {
        this.$refs.form.init(id);
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedDatas = selection.map(item => item)
      //获取所有选中项数组的长度
      this.selectedNum = selection.length
    },
    // 导出
    exportHandle() {
      this.downExports = true;
      this.$refs.export.init('/api/order/pur/progressExport', '订单执行进度表',this.queryParam);
    },
    // 搜索方法，并返回到第一页
    search() {
      this.queryParam.page = 1;
      this.initData();
    },
    // 重置方法
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.search();
    },
    // Form表单关闭时回调方法
    closeForm() {
      this.formVisible = false;
      this.initData();
    },
  }
}
</script>

<style scoped>

</style>
