{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\tenant\\index.vue", "mtime": 1754019228380}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\n\r\nimport {dFlowMixin} from \"@dian/dian-ui-vue\";\r\nimport Form from '@/views/order/produce/tenant/Form';\r\nimport store from \"@/store\";\r\nimport {getTenantSaleOrderProgressList} from \"@/api/order/sale\";\r\nimport dian from \"@/utils/dian\";\r\n\r\nexport default {\r\n  //加载底层公有组件\r\n  mixins: [dFlowMixin],\r\n  name: \"order-progress-tenant\",\r\n  components: {\r\n    Form\r\n  },\r\n  data() {\r\n    return {\r\n      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型\r\n      orderLineStat: store.getters.commonEnums['order.PurlineStatEnum'], // 订单明细单据状态\r\n      validStat: store.getters.commonEnums['comm.ValidEnum'], // 订单明细单据状态\r\n      selectTypeOptions: [ // 查询类型（完成状态）\r\n        {\r\n          key: '1',\r\n          value: '未完成'\r\n        },\r\n        {\r\n          key: '2',\r\n          value: '已完成'\r\n        },\r\n        {\r\n          key: '3',\r\n          value: '全部'\r\n        }\r\n      ],\r\n      queryParam: {\r\n        page: 1,\r\n        limit: 20,\r\n        orderNo : '', // 订单号\r\n        vendor : '', // 供应商编码|名称\r\n        goods : '',  // 物料编码|名称|描述|图号\r\n        orderDate : '',  // 订单日期\r\n        startDate : '',  // 订单日期 - 开始日期\r\n        endDate : '',  // 订单日期 - 结束日期\r\n        orderType : '', // 订单类型\r\n        itemStat: '', // 单据状态\r\n        deptId:'',//组织机构id\r\n        isTenant : 'ture', // 是否是采购方进入\r\n        isDESC : 'ture', // 是否倒叙排序\r\n        purName:'',//采购员名称\r\n        returnMark: null, // 是否退货\r\n        freeMark: null, // 是否免费\r\n        selectType: '3', // 默认查询显示全部的数据\r\n      },\r\n      showAll: false,\r\n      formVisible: false,\r\n      listLoading: false,\r\n      btnLoading: false,\r\n      downExports: false,  // true-下载模板，false-导出\r\n      list: [],\r\n      total: 0\r\n    }\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function handler(to, from) {\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.listLoading = true;\r\n      if (this.queryParam.orderDate.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');\r\n        this.queryParam.startDate = startDate;\r\n        this.queryParam.endDate = endDate;\r\n      } else {\r\n        this.queryParam.startDate = '';\r\n        this.queryParam.endDate = '';\r\n      }\r\n      let subCompanyInfoData = dian.storageGet('subCompanyInfo');\r\n      if (subCompanyInfoData){\r\n        this.queryParam.deptId = subCompanyInfoData.id;\r\n      }\r\n      const date = this.queryParam.orderDate;\r\n      this.queryParam.orderDate = '';\r\n      getTenantSaleOrderProgressList(this.queryParam).then(res => {\r\n        this.total = res.data.totalCount;\r\n        this.list = res.data.list;\r\n        this.queryParam.orderDate = date;\r\n        this.listLoading = false;\r\n      }).catch(() => {\r\n        this.queryParam.orderDate = date;\r\n        this.listLoading = false;\r\n      })\r\n    },\r\n    // 新增|编辑 项目报备\r\n    addEditOrderHandle(id) {\r\n      this.formVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.init(id);\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedDatas = selection.map(item => item)\r\n      //获取所有选中项数组的长度\r\n      this.selectedNum = selection.length\r\n    },\r\n    // 导出\r\n    exportHandle() {\r\n      this.downExports = true;\r\n      this.$refs.export.init('/api/order/pur/progressExport', '订单执行进度表',this.queryParam);\r\n    },\r\n    // 搜索方法，并返回到第一页\r\n    search() {\r\n      this.queryParam.page = 1;\r\n      this.initData();\r\n    },\r\n    // 重置方法\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.search();\r\n    },\r\n    // Form表单关闭时回调方法\r\n    closeForm() {\r\n      this.formVisible = false;\r\n      this.initData();\r\n    },\r\n  }\r\n}\r\n", null]}