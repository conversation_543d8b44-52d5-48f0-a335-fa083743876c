{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\vendor\\index.vue", "mtime": 1754027118419}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\n\r\nimport {dFlowMixin} from \"@dian/dian-ui-vue\";\r\nimport Form from '@/views/order/produce/vendor/Form';\r\nimport store from \"@/store\";\r\nimport {getVendorSaleOrderProgressList} from \"@/api/order/sale\";\r\nimport dian from \"@/utils/dian\";\r\nimport CodePrintTemplate from './codePrintTemplate'\r\n\r\nexport default {\r\n  //加载底层公有组件\r\n  mixins: [dFlowMixin],\r\n  name: \"order-progress-vendor\",\r\n  components: {\r\n    Form,\r\n    CodePrintTemplate,\r\n  },\r\n  data() {\r\n    return {\r\n      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型\r\n      queryParam: {\r\n        page: 1,\r\n        limit: 20,\r\n        keyword : '',  // 订单号/物料编码/物料名称/物料描述\r\n        orderDate : '',  // 订单日期\r\n        dept:'',\r\n        orderNo:'',\r\n        goods:'',\r\n        bsart : '', // 采购类型\r\n      },\r\n      formVisible: false,\r\n      listLoading: false,\r\n      btnLoading: false,\r\n      templateVisible: false,\r\n      list: [],\r\n      total: 0\r\n    }\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.listLoading = true;\r\n      if (this.queryParam.orderDate.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');\r\n        this.queryParam.orderDate = startDate +\" 至 \"+endDate;\r\n      }\r\n      let subCompanyInfoData = dian.storageGet('subCompanyInfo');\r\n      if (subCompanyInfoData){\r\n        this.queryParam.deptId = subCompanyInfoData.id;\r\n      }\r\n      getVendorSaleOrderProgressList(this.queryParam).then(res => {\r\n        this.total = res.page.totalCount;\r\n        this.list = res.page.list;\r\n        this.listLoading = false;\r\n      }).catch(() => {\r\n        this.listLoading = false;\r\n      })\r\n    },\r\n    // 新增|编辑 项目报备\r\n    addEditOrderHandle(id) {\r\n      this.formVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.init(id);\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedDatas = selection.map(item => item)\r\n      //获取所有选中项数组的长度\r\n      this.selectedNum = selection.length\r\n    },\r\n    // 导出\r\n    exportHandle() {\r\n      // 当导出为全部时，赋予总数为最大值\r\n      this.queryParam.total = this.total;\r\n      this.$refs.export.init('/api/order/sale/saleProgressExport', '订单执行进度表',this.queryParam);\r\n    },\r\n    // 搜索方法，并返回到第一页\r\n    search() {\r\n      this.queryParam.page = 1;\r\n      this.initData();\r\n    },\r\n    // 重置方法\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.search();\r\n    },\r\n    // Form表单关闭时回调方法\r\n    closeForm() {\r\n      this.formVisible = false;\r\n      this.initData();\r\n    },\r\n    // 打印条码\r\n    printBarCode(data) {\r\n      this.templateVisible = true\r\n      let params = {\r\n        id: null,\r\n        tenantPId: 0,\r\n        templateName: '',\r\n        sceneId: null,\r\n        sceneCode: '',\r\n        sceneName: '',\r\n        remark: '',\r\n        printJson: '',\r\n        goodsName:data.goodsName,\r\n        goodsCode:data.goodsErpCode,\r\n        vendorName:data.vendorName,\r\n        vendorCode:data.vendorCode,\r\n        devSumNum: 0, // 总送货数\r\n        PackagingNum: 0, // 每箱数量\r\n        numberOfBoxes: 0, // 箱数\r\n        countless: 0, // 尾数\r\n        prodDate: null, // 生产日期\r\n        warrantyPeriod: '', // 质保天数\r\n        warrantyDate: '', // 质保日期\r\n        prodBatchNo: '', // 批次号\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.PrintTemplate.goodsCode=data.goodsCode;\r\n        this.$refs.PrintTemplate.isBatchPrint = false;\r\n        this.$refs.PrintTemplate.printData = params;\r\n        this.$refs.PrintTemplate.dataForm = params;\r\n        this.$refs.PrintTemplate.init(\"deliveryNotePrinting\")\r\n      })\r\n    },\r\n  }\r\n}\r\n", null]}