<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dian.modules.order.dao.SaleDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dian.modules.order.entity.PurEntity" id="saleMap">
        <result property="tenantPId" column="tenant_p_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="sourceTenantId" column="source_tenant_id"/>
        <result property="id" column="id"/>
    </resultMap>

    <select id="getSaleList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            op.id,op.bsart,op.pur_no,op.vendor_id,op.vendor_code,op.vendor_name,op.dept_id,op.dept_code,op.dept_name,op.order_date,op.source_no,
            op.sync_date,op.order_type,op.stat,op.reply_stat,op.change_count,op.remark,op.tenant_id,op.tenant_name,
            op.creater,op.modifier,op.create_date,op.create_id,op.modify_date,op.modifi_id,
            op.delete_flag,op.create_id,op.creater,op.create_date,op.modifi_id,op.modifier,
            op.modify_date, opl.id as line_id,opl.pur_id,opl.goods_id,opl.goods_erp_code,opl.goods_code,opl.goods_name,
            opl.goods_model,opl.uom_id,opl.uom_name,opl.rate_id,opl.rate_name,opl.rate_val,opl.currency_id,opl.currency_name,opl.taxes_type,opl.invoice_type,
            opl.gst_price,opl.tax_price,opl.barcode_type,opl.warehouse_id,opl.warehouse_code,opl.warehouse_name,opl.soure_no,opl.change_count
            as line_change_count,opl.item_stat,opl.delivery_date,opl.sign,
            opl.reply_date,opl.purchase_remark,opl.vendor_remark,opl.make_num,opl.order_num line_order_sum,opl.purchase_confirm,opl.vendor_confirm,opl.purchase_doc_url
            ,opl.vendor_doc_url,opl.fix_num,opl.line_remark,
            opl.wait_num,opl.create_date,opl.is_main,opl.delivery_type,opl.main_item_id,opl.big_pack_standard_num,opl.small_pack_standard_num,opl.big_pack_label_num,opl.small_pack_label_num
            ,opl.big_pack_mantissa,opl.small_pack_mantissa,op.reserved06,op.purchasing_group,
            IFNULL(opl.order_num,0)-IFNULL(opl.make_num,0) as canMakeNum,
            opl.pur_employee_name,opl.sale_employee_name,opl.goods_class_name,opl.delivery_stat,opl.delete_flag as
            line_delete_flag,opl.create_id as line_create_id,opl.creater as line_creater
            ,opl.create_date as line_create_date,opl.modifi_id as line_modifi_id,
            opl.modifier as line_modifier,opl.modify_date as line_modify_date,opl.seq,opl.tax_Amount,opl.gst_Amount,opl.main_item_code,ifnull(opl.drawing_no,'-') as drawing_no
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        <!--
            oper_type 关注或急单 1为关注
            order_type 订单类型 2为样品订单
            delete_flag 删除标志 0否（No） 1是（Yes）
            这里的orderId与销售订单明细表opl的id进行关联
            isFollow 和 x1 都是别名
        -->
        left join (
            select
            jof.order_id,
            sum(
                case when jof.follow_id= #{params.userId}
                and jof.oper_type=1
                and jof.order_type=2
                and jof.delete_flag=0 then 1 else 0 end
            ) isFollow
            from order_follow jof
            group by  jof.order_id
        )x1 on x1.order_id = opl.id
        <where>
            and op.stat &gt; 2
            <!-- 模糊条件查询 -->
            <if test="params.keyword != null and params.keyword != ''">
                <!--
                  条件如下：↓
                      销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称
                      销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号 soure_no、行备注 line_remark
                -->
                and (
                  op.pur_no like CONCAT('%',#{params.keyword},'%')
                  or op.tenant_id like CONCAT('%',#{params.keyword},'%')
                  or op.tenant_name like CONCAT('%',#{params.keyword},'%')
                  or opl.goods_name like CONCAT('%',#{params.keyword},'%')
                  or opl.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                  or opl.goods_model like CONCAT('%',#{params.keyword},'%')
                  or opl.soure_no like CONCAT('%',#{params.keyword},'%')
                  or opl.line_remark like CONCAT('%',#{params.keyword},'%')
                  or opl.drawing_no like CONCAT('%',#{params.keyword},'%')
                )
            </if>
            <if test="params.saleNo !=null and params.saleNo != ''">
                and op.pur_no like CONCAT('%',#{params.saleNo},'%')
            </if>
            <if test="params.vendorId !=null and params.vendorId != ''">
                and op.vendor_id = #{params.vendorId}
            </if>
            <if test="params.itemStat !=null and params.itemStat != ''">
                and opl.item_stat = #{params.itemStat}
            </if>
            <if test="params.reserved06 !=null and params.reserved06 != ''">
                and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
            </if>
            <if test="params.vendorCode !=null and params.vendorCode != ''">
                and (op.vendor_code like CONCAT('%',#{params.vendorCode},'%') or op.vendor_name like
                CONCAT('%',#{params.vendorCode},'%'))
            </if>
            <if test="params.orderType !=null and params.orderType != '' and params.orderType !=99">
                and op.order_type = #{params.orderType}
            </if>
            <if test="params.sorderDate != null and params.sorderDate != ''">
                <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.eorderDate != null and params.eorderDate != ''">
                <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.whereType !=null and params.whereType != '' and params.whereType != 100">
                <choose>
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and opl.item_stat=1 and op.stat &gt; 2
                    </when>
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        and op.reply_stat=1 and now()>opl.delivery_date
                    </when>
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and opl.item_stat=2
                    </when>
                    <when test="params.whereType == 4 or params.whereType == '4'">
                    </when>
                    <when test="params.whereType == 5 or params.whereType == '5'">
                        and (datediff(DATE_FORMAT(delivery_date,'%Y-%m-%d'),DATE_FORMAT(order_date,'%Y-%m-%d')) between
                        0 and 3 or
                        (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and
                        jof.order_type=2 and jof.order_id=op.id and jof.delete_flag=0)&gt;0)
                        and   (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and
                        jof.order_type=2 and jof.order_id=op.id and jof.delete_flag=1)&lt;1
                    </when>
                    <when test="params.whereType == 6 or params.whereType == '6'">
                        and (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and
                        jof.order_type=2 and jof.oper_type=1 and jof.order_id=op.id and jof.delete_flag=0)>0
                    </when>
                    <when test="params.whereType == 7 or params.whereType == '7'">
                        and opl.item_stat=4 and fix_num=0
                    </when>
                    <when test="params.whereType == 8 or params.whereType == '8'">
                        and opl.item_stat=4 and fix_num=0 and now()>opl.reply_date
                    </when>
                    <when test="params.whereType == 9 or params.whereType == '9'">
                        and opl.item_stat=4 and opl.wait_num&gt;0
                    </when>
                    <when test="params.whereType == 10 or params.whereType == '10'">
                        and opl.item_stat=4 and opl.wait_num=0
                    </when>
                    <when test="params.whereType == 11 or params.whereType == '11'">
                        and op.stat &lt;=2
                    </when>
                    <when test="params.whereType == 12 or params.whereType == '12'">
                        and opl.item_stat=4 and fix_num=0
                    </when>
                    <!-- 我关注的 >0 就代表是被关注的-->
                    <when test="params.whereType == 13 or params.whereType == 13">
                        and x1.isFollow &gt; 0
                    </when>
                    <!-- 急单 -->
                    <when test="params.whereType == 14 or params.whereType == 14">
                        and opl.is_busy = 1
                    </when>
                    <!-- 已确认 -->
                    <when test="params.whereType == 15 or params.whereType == '15'">
                        and opl.item_stat = 4
                    </when>
                    <!--&lt;!&ndash; 我的 &ndash;&gt;-->
                    <!--<when test="params.whereType == 13 or params.whereType == 13">-->
                        <!--and pur_employee_name = #{params.userName}-->
                    <!--</when>-->
                </choose>
            </if>
        </where>
        order by op.id desc
    </select>

    <select id="mainConfirmCountSaleMap" parameterType="java.util.HashMap" resultType="java.lang.Long">
        select
       count(*) from
         order_pur op where op.delete_flag=0
        and op.stat > 2
        <!-- 模糊条件查询 -->
        <if test="params.keyword != null and params.keyword != ''">
            <!--
              条件如下：↓
                  销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称 tenant_name
                  销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号soure_no、行备注line_remark
            -->
            and (
            op.pur_no like CONCAT('%',#{params.keyword},'%')
            or op.tenant_name like CONCAT('%',#{params.keyword},'%')
            )
        </if>
        <if test="params.reserved06 !=null and params.reserved06 != ''">
            and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
        </if>
        <!-- 根据供应商的id来校验 -->
        <if test="params.vendorId !=null and params.vendorId != ''">
            and op.vendor_id = #{params.vendorId}
        </if>

        <!-- 根据订单表中的订单类型作为条件进行查询，销售方是orderType为2的 采购方是orderType为1的 -->
        <if test="params.orderType !=null and params.orderType != '' and params.orderType !=99">
            and op.order_type = #{params.orderType}
        </if>
        <if test="params.bsart !=null and params.bsart != ''">
            and op.bsart = #{params.bsart}
        </if>

        <!-- 根据订单日期查询 - 订单日期范围过滤 - sorderDate起始时间 - eorderDate结束时间 -->
        <if test="params.sorderDate != null and params.sorderDate != ''">
            <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
        </if>
        <if test="params.eorderDate != null and params.eorderDate != ''">
            <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
        </if>

        <!-- 销售订单列表气泡的动态查询条件 -->
        <if test="params.whereType !=null and params.whereType != '' and params.whereType != 100">
            <choose>
                <!--新单-->
                <when test="params.whereType == 1 or params.whereType == '1'">
                    and op.stat = 3
                </when>
                <!--超时未答交，超时时间从绩效模块取值：答交及时率-->
                <when test="params.whereType == 2 or params.whereType == '2'">
                    <![CDATA[
                            and op.stat=3 and TIMESTAMPDIFF(MINUTE,publish_date,now()) > #{params.replyMinute}
                        ]]>
                </when>
                <!--答交异常-->
                <when test="params.whereType == 3 or params.whereType == '3'">
                    and op.stat=4
                </when>
                <!--已确认-->
                <when test="params.whereType == 4 or params.whereType == '4'">
                    and op.stat = 5
                </when>
                <!--全部-->
                <when test="params.whereType == 5 or params.whereType == '5'">
                </when>
            </choose>
        </if>
    </select>

    <!-- 销售订单应答列表模式查询 -->
    <select id="findMainSaleList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        op.tenant_id,
        op.id,op.pur_no,op.vendor_id,op.vendor_code,op.vendor_name,op.dept_name,op.order_date,op.dep_code,op.bsart,op.pur_name,
        op.order_flag,op.sync_date,op.order_type,op.stat,op.reply_stat,op.change_count,op.remark,op.tenant_name,op.currency_name,op.total_amount,op.order_remark,
        op.publish_date,op.purchasing_group,op.reserved06,op.purchasing_group,
        opi.seq,
        -- 明细表
        opi.item_stat,
        op.id sale_id,
        opi.id sale_item_id,
        op.pur_no sale_no,
        (opi.order_num - opi.make_num) dev_num,
        opi.is_close,
        opi.erp_change_type,
        opi.goods_id,
        opi.goods_erp_code,
        opi.goods_code,
        opi.goods_name,
        opi.goods_model,
        opi.delivery_stat,
        opi.order_num,
        opi.matched_plan_num,
        opi.make_num,
        opi.fix_num,
        opi.wait_num,
        opi.receive_num,
        opi.refund_num,
        opi.ref_ded_num,
        opi.erp_master_num,
        opi.erp_reject_num,
        opi.ret_ded_num,
        opi.rate_name,
        opi.delivery_date,
        opi.tax_price,
        opi.gst_price,
        opi.gst_amount,
        opi.source_id,
        opi.uom_id,
        opi.uom_code,
        opi.uom_name,
        op.pur_id,
        op.pur_Name,
        op.dept_id,
        op.dept_name,
        op.dept_code,
        opi.delivery_type,
        opi.rate_id,
        opi.rate_val,
        opi.currency_id,
        opi.currency_name
        from order_pur op left join order_pur_item opi on op.id = opi.pur_id
        where op.delete_flag = 0
        AND opi.delete_flag = 0
        AND opi.is_close = 0
        and op.stat > 2
        <!-- 采购订单号 -->
        <if test="params.purNo !=null and params.purNo != ''">
            and op.pur_no like CONCAT('%',#{params.purNo},'%')
        </if>
        <!-- 单据状态 -->
        <if test="params.stat !=null and params.stat != ''">
            and op.stat = #{params.stat}
        </if>
        <!-- 物料编码/名称/规格 -->
        <if test="params.goods !=null and params.goods != ''">
            and (
            opi.goods_code like CONCAT('%',#{params.goods},'%')
            or opi.goods_erp_code like CONCAT('%',#{params.goods},'%')
            or opi.goods_name like CONCAT('%',#{params.goods},'%')
            or opi.goods_model like CONCAT('%',#{params.goods},'%')
            )
        </if>
        <!-- 采购员名称 -->
        <if test="params.purName !=null and params.purName != ''">
            and op.pur_name like CONCAT('%',#{params.purName},'%')
        </if>
        <!-- 根据订单日期查询 - 订单日期范围过滤 - sorderDate起始时间 - eorderDate结束时间 -->
        <if test="params.sorderDate != null and params.sorderDate != ''">
            <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
        </if>
        <if test="params.eorderDate != null and params.eorderDate != ''">
            <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
        </if>

            <!-- 模糊条件查询 -->
            <if test="params.keyword != null and params.keyword != ''">
                <!--
                  条件如下：↓
                      销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称 tenant_name
                      销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号soure_no、行备注line_remark
                -->
                and (
                op.pur_no like CONCAT('%',#{params.keyword},'%')
                or op.tenant_name like CONCAT('%',#{params.keyword},'%')
                )
            </if>

            <!-- 根据供应商的id来校验 -->
            <if test="params.vendorId !=null and params.vendorId != ''">
                and op.vendor_id = #{params.vendorId}
            </if>
            <if test="params.reserved06 !=null and params.reserved06 != ''">
                and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
            </if>
            <!-- 根据订单表中的订单类型作为条件进行查询，销售方是orderType为2的 采购方是orderType为1的 -->
            <if test="params.orderType !=null and params.orderType != '' and params.orderType !=99">
                and op.order_type = #{params.orderType}
            </if>
            <if test="params.bsart !=null and params.bsart != ''">
                and op.bsart = #{params.bsart}
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                and op.dept_id = #{params.deptId}
            </if>
            <if test="params.dept != null and params.dept != ''">
                and (op.dept_code like CONCAT('%',#{params.dept},'%') OR op.dept_name like CONCAT('%',#{params.dept},'%'))
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and (op.order_no like CONCAT('%',#{params.orderNo},'%'))
            </if>

            <!-- 销售订单列表气泡的动态查询条件 -->
            <if test="params.whereType !=null and params.whereType != '' and params.whereType != 100">
                <choose>
                    <!--新单-->
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and op.stat = 3
                    </when>
                    <!--超时未答交，超时时间从绩效模块取值：答交及时率-->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        <![CDATA[
                            and op.stat=3 and TIMESTAMPDIFF(MINUTE,publish_date,now()) > #{params.replyMinute}
                        ]]>
                    </when>
                    <!--答交异常-->
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and op.stat=4
                    </when>
                    <!--已确认-->
                    <when test="params.whereType == 4 or params.whereType == '4'">
                        and op.stat = 5
                    </when>
                    <!--全部-->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                    </when>
                </choose>
            </if>
        <!-- 默认根据销售订单id来进行降序 -->
        order by op.id desc,opi.seq ASC
    </select>


    <!-- 销售订单列表查询 -->
    <select id="findSaleList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            op.bsart,
            op.id,op.pur_no,op.vendor_id,op.vendor_code,op.vendor_name,op.dept_id,op.dept_code,op.dept_name,op.order_date,op.source_no,
            op.sync_date,op.order_type,op.stat,op.reply_stat,op.change_count,op.remark,op.purchasing_group,op.tenant_id,op.tenant_name,
            op.creater,op.modifier,op.create_date,op.create_id,op.modify_date,op.modifi_id,
            op.delete_flag,op.create_id,op.creater,op.create_date,op.modifi_id,op.modifier,
            op.modify_date, opl.id as line_id,opl.pur_id,opl.pur_id,opl.goods_id,opl.goods_erp_code,opl.goods_code,opl.goods_name,opl.price_uom,
            opl.goods_model,opl.uom_id,opl.uom_name,opl.rate_id,opl.rate_name,opl.rate_val,opl.currency_id,opl.currency_name,opl.taxes_type,opl.invoice_type,
            opl.gst_price,opl.tax_price,opl.barcode_type,opl.warehouse_id,opl.warehouse_code,opl.warehouse_name,opl.soure_no,opl.change_count
            as line_change_count,opl.item_stat,opl.delivery_date,opl.sign,
            opl.reply_date,opl.purchase_remark,opl.vendor_remark,opl.make_num,opl.order_num
            line_order_sum,opl.purchase_confirm,opl.vendor_confirm,opl.purchase_doc_url
            ,opl.vendor_doc_url,opl.fix_num,
            opl.wait_num,opl.create_date,opl.is_main,opl.delivery_type,opl.main_item_id,opl.big_pack_standard_num,opl.small_pack_standard_num,opl.big_pack_label_num,opl.small_pack_label_num
            ,opl.big_pack_mantissa,opl.small_pack_mantissa,
            IFNULL(opl.order_num,0)-IFNULL(opl.make_num,0) as can_Make_Num,
            opl.pur_employee_name,opl.sale_employee_name,opl.goods_class_name,opl.delivery_stat,opl.delete_flag as
            line_delete_flag,opl.create_id as line_create_id,opl.creater as line_creater
            ,opl.create_date as line_create_date,opl.modifi_id as line_modifi_id,
            opl.modifier as line_modifier,opl.modify_date as line_modify_date,opl.seq,opl.tax_Amount,opl.gst_Amount,opl.main_item_code,ifnull(opl.drawing_no,'-') as drawing_no
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        and op.stat > 2
        <!--
            左连接查询
            oper_type 关注或急单 1为关注
            order_type 订单类型 2为样品订单
            delete_flag 删除标志 0否（No） 1是（Yes）
            这里的orderId与销售订单明细表opl的id进行关联
            isFollow 和 x1 都是别名
        -->
        left join (
            select
            jof.order_id,
            sum(
                case when jof.follow_id= #{params.userId}
                and jof.oper_type = 1
                and jof.order_type = 2
                and jof.delete_flag = 0 then 1 else 0 end
        ) isFollow
        from order_follow jof
        group by  jof.order_id
        )x1 on x1.order_id = opl.id
        <where>
            <!-- 模糊条件查询 -->
            <if test="params.keyword != null and params.keyword != ''">
                <!--
                  条件如下：↓
                      销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称 tenant_name
                      销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号soure_no、行备注line_remark
                -->
                and (
                op.pur_no like CONCAT('%',#{params.keyword},'%')
                or op.tenant_id like CONCAT('%',#{params.keyword},'%')
                or op.tenant_name like CONCAT('%',#{params.keyword},'%')
                or opl.goods_name like CONCAT('%',#{params.keyword},'%')
                or opl.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                or opl.goods_model like CONCAT('%',#{params.keyword},'%')
                or opl.soure_no like CONCAT('%',#{params.keyword},'%')
                or opl.line_remark like CONCAT('%',#{params.keyword},'%')
                or opl.drawing_no like CONCAT('%',#{params.keyword},'%')
                )
            </if>

            <!-- 根据供应商的id来校验 -->
            <if test="params.vendorId !=null and params.vendorId != ''">
                and op.vendor_id = #{params.vendorId}
            </if>
            <if test="params.reserved06 !=null and params.reserved06 != ''">
                and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
            </if>
            <!-- 根据订单表中的订单类型作为条件进行查询，销售方是orderType为2的 采购方是orderType为1的 -->
            <if test="params.orderType !=null and params.orderType != '' and params.orderType !=99">
                and op.order_type = #{params.orderType}
            </if>
            <if test="params.bsart !=null and params.bsart != ''">
                and op.bsart = #{params.bsart}
            </if>

            <!-- 根据订单日期查询 - 订单日期范围过滤 - sorderDate起始时间 - eorderDate结束时间 -->
            <if test="params.sorderDate != null and params.sorderDate != ''">
                <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.eorderDate != null and params.eorderDate != ''">
                <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>

            <!-- 销售订单列表气泡的动态查询条件 -->
            <if test="params.whereType !=null and params.whereType != '' and params.whereType != 100">
                <choose>
                    <!--新单-->
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and opl.item_stat = 1
                    </when>
                    <!--超时未答交，超时时间从绩效模块取值：答交及时率-->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        <![CDATA[
                            and opl.item_stat=1 and TIMESTAMPDIFF(MINUTE,publish_date,now()) > #{params.replyMinute}
                        ]]>
                    </when>
                    <!--答交异常-->
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and opl.item_stat=2
                    </when>
                    <!--已确认-->
                    <when test="params.whereType == 4 or params.whereType == '4'">
                        and opl.item_stat = 4
                    </when>
                    <!--全部-->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                    </when>
                    <!-- 急单 -->
                    <when test="params.whereType == 6 or params.whereType == '6'">
                        and opl.is_busy = 1
                    </when>
                    <!-- 我关注的 >0 就代表是被关注的-->
                    <when test="params.whereType == 7 or params.whereType == '7'">
                        and x1.isFollow &gt; 0
                    </when>

                </choose>
            </if>
        </where>
        <!-- 默认根据销售订单id来进行降序 -->
        order by op.id desc
    </select>

    <!-- 销售订单进度列表查询 -->
    <select id="findSaleProgressList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            opl.werks,opl.order_price_uom,opl.class_code goods_class_code,opl.goods_class_name,
            op.bsart,
            op.id,op.pur_no,op.vendor_id,op.vendor_code,op.vendor_name,op.dept_id,op.dept_code,op.dept_name,op.order_date,op.source_no,
            op.sync_date,op.order_type,op.stat,op.reply_stat,op.change_count,op.remark,op.purchasing_group,op.tenant_id,op.tenant_name,
            op.creater,op.modifier,op.create_date,op.create_id,op.modify_date,op.modifi_id,
            op.delete_flag,op.create_id,op.creater,op.create_date,op.modifi_id,op.modifier,
            op.modify_date, opl.id as line_id,opl.pur_id,opl.goods_id,opl.goods_erp_code,opl.goods_code,opl.goods_name,opl.price_uom,
            opl.goods_model,opl.uom_id,opl.uom_code,opl.uom_name,opl.rate_id,opl.rate_name,opl.rate_val,opl.currency_id,opl.currency_name,opl.taxes_type,opl.invoice_type,
            opl.gst_price,opl.tax_price,opl.barcode_type,opl.warehouse_id,opl.warehouse_code,opl.warehouse_name,opl.soure_no,opl.change_count
            as line_change_count,opl.item_stat,opl.delivery_date,opl.sign,opl.line_remark,
            opl.reply_date,opl.purchase_remark,opl.vendor_remark,
            opl.make_num,opl.order_num line_order_sum,opl.fix_num,opl.receive_num,opl.refund_num,opl.erp_master_num,opl.erp_reject_num,
            opl.purchase_confirm,opl.vendor_confirm,opl.purchase_doc_url ,opl.vendor_doc_url,
            opl.wait_num,opl.create_date,opl.is_main,opl.delivery_type,opl.main_item_id,opl.big_pack_standard_num,opl.small_pack_standard_num,opl.big_pack_label_num,opl.small_pack_label_num
            ,opl.big_pack_mantissa,opl.small_pack_mantissa,
            IFNULL(opl.order_num,0)-IFNULL(opl.make_num,0) as canMakeNum,
            opl.pur_employee_name,opl.sale_employee_name,opl.goods_class_name,opl.delivery_stat,opl.delete_flag as
            line_delete_flag,opl.create_id as line_create_id,opl.creater as line_creater
            ,opl.create_date as line_create_date,opl.modifi_id as line_modifi_id, opl.is_close,
            opl.modifier as line_modifier,opl.modify_date as line_modify_date,opl.seq,opl.tax_Amount,
            opl.gst_Amount,opl.main_item_code,ifnull(opl.drawing_no,'-') as drawing_no,opl.ref_ded_num,opl.ret_ded_num,
            opl.matched_plan_num,opl.un_competent_num,opl.aux_uom_id,opl.aux_uom_code,opl.aux_uom_name,op.pur_name
        from order_pur_item opl inner join  order_pur op
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        and opl.item_stat = 4
        <!--
            oper_type 关注或急单 1为关注
            order_type 订单类型 2为供方关注的
            delete_flag 删除标志 0否（No） 1是（Yes）
            这里的orderId与销售订单明细表opl的id进行关联
            isFollow 和 x1 都是别名
        -->
        left join (
            select
            jof.order_id,
            sum(
            case when jof.follow_id= #{params.userId}
            and jof.oper_type=1
            and jof.order_type=2
            and jof.delete_flag=0 then 1 else 0 end
        ) isFollow
        from order_follow jof
        group by  jof.order_id
        )x1 on x1.order_id = opl.id
        <where>
            <!-- 肖来注释 进货执行进度表去掉主表状态搜索
             and op.stat&gt;2
            -->
            <!-- 模糊条件查询 -->
            <if test="params.keyword != null and params.keyword != ''">
                <!--
                  条件如下：↓
                      销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称 tenant_name
                      销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号soure_no、行备注line_remark
                -->
                and (
                     op.pur_no like CONCAT('%',#{params.keyword},'%')
                    <!--
                        肖来注释
                     or op.tenant_id like CONCAT('%',#{params.keyword},'%')
                     or op.tenant_name like CONCAT('%',#{params.keyword},'%'-->
                     or opl.goods_name like CONCAT('%',#{params.keyword},'%')
                     or opl.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                     or opl.goods_model like CONCAT('%',#{params.keyword},'%')
                     or opl.soure_no like CONCAT('%',#{params.keyword},'%')
                     or opl.line_remark like CONCAT('%',#{params.keyword},'%')
                     or opl.drawing_no like CONCAT('%',#{params.keyword},'%')
                )
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and op.pur_no LIKE CONCAT('%',#{params.orderNo},'%')
            </if>
     <!-- 根据订单类型查询 -->
            <if test="params.orderType != null and params.orderType  != '' and params.orderType != 99">
                and op.order_type = #{params.orderType}
            </if>
            <if test="params.bsart !=null and params.bsart != ''">
                and op.bsart = #{params.bsart}
            </if>
            <if test="params.reserved06 !=null and params.reserved06 != ''">
                and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
            </if>
            <!-- 根据采购方的id来查询数据 -->
            <if test="params.vendorId !=null and params.vendorId != ''">
                and op.vendor_id = #{params.vendorId}
            </if>

            <!-- 根据订单时间查询 查询一个时间段区间的订单信息 如：查询2021/7/10 - 2021/7/23的订单 -->
            <if test="params.sorderDate != null and params.sorderDate != ''">
                <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.eorderDate != null and params.eorderDate != ''">
                <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                and op.dept_id = #{params.deptId}
            </if>
            <if test="params.dept != null and params.dept != ''">
                and (op.dept_code like CONCAT('%',#{params.dept},'%') OR op.dept_name like CONCAT('%',#{params.dept},'%'))
            </if>
            <if test="params.goods != null and params.goods != ''">
                and (opl.goods_code like CONCAT('%',#{params.goods},'%')
                         OR opl.goods_erp_code like CONCAT('%',#{params.goods},'%')
                         OR opl.goods_name like CONCAT('%',#{params.goods},'%')
                         OR opl.goods_model like CONCAT('%',#{params.goods},'%')
                    )
            </if>
            <!-- 动态的气泡过滤条件 -->
            <if test="params.whereType !=null and params.whereType != ''">
                <choose>
                    <!--未送货-->
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and opl.item_stat=4 and fix_num=0
                    </when>
                    <!--超时未送货-->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                            and opl.item_stat=4 and fix_num=0 and now()&gt;opl.reply_date
                    </when>
                    <!--未完成-->
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and opl.order_num&lt;&gt;(opl.erp_master_num-opl.erp_reject_num)
                    </when>
                    <!--已完成-->
                    <when test="params.whereType == 4 or params.whereType == '4'">
                        and opl.order_num=(opl.erp_master_num-opl.erp_reject_num)
                    </when>
                    <!--全部-->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                    </when>
                    <!-- 急单 -->
                    <when test="params.whereType == 6 or params.whereType == 6">
                        and opl.is_busy = 1
                    </when>
                    <!-- 我关注的 >0 就代表是被关注的-->
                    <when test="params.whereType == 7 or params.whereType == 7">
                        and x1.isFollow &gt; 0
                    </when>

                </choose>
            </if>
        </where>
        order by op.id desc
    </select>

    <!--销售订单和销售订单进度列表的气泡数-->
    <select id="countMap" parameterType="java.util.HashMap" resultType="java.lang.Long">
        SELECT
        count(*) orderCount
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        <!--
            oper_type 关注或急单 1为关注
            order_type 订单类型 1为普通订单
            delete_flag 删除标志 0否（No） 1是（Yes）
            这里的orderId与销售订单明细表opl的id进行关联
            isFollow 和 x1 都是别名
        -->
        left join (
            select
            jof.order_id,
            sum(case when jof.follow_id=#{params.userId}
            and jof.oper_type = 1
            and jof.order_type = 2
            and jof.delete_flag = 0 then 1 else 0 end
        ) isFollow
        from  order_follow jof
        group by jof.order_id
        )x1 on x1.order_id = opl.id
      <where>
         and op.stat&gt;2
          <if test="params.saleNo !=null and params.saleNo != ''">
              and op.pur_no like CONCAT('%',#{params.saleNo},'%')
          </if>
          <if test="params.vendorId !=null and params.vendorId != ''">
              and op.vendor_id = #{params.vendorId}
          </if>
          <if test="params.vendorCode !=null and params.vendorCode != ''">
              and (op.vendor_code like CONCAT('%',#{params.vendorCode},'%') or op.vendor_name like
              CONCAT('%',#{params.vendorCode},'%'))
          </if>
          <if test="params.orderType !=null and params.orderType != '' and params.orderType !=99">
              and op.order_type = #{params.orderType}
          </if>
          <if test="params.itemStat !=null and params.itemStat != ''">
              and opl.item_stat = #{params.itemStat}
          </if>
<!--          <if test="params.orderNo != null and orderNo != ''">-->
<!--              and op.pur_no LIKE CONCAT('%',#{params.orderNo},'%')-->
<!--          </if>-->
        <if test="params.sorderDate != null and params.sorderDate != ''">
            <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
        </if>
        <if test="params.eorderDate != null and params.eorderDate != ''">
            <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
        </if>
          <if test="params.reserved06 !=null and params.reserved06 != ''">
              and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
          </if>
          <if test="params.deptId != null and params.deptId != ''">
              and op.dept_id = #{params.deptId}
          </if>
          <if test="params.dept != null and params.dept != ''">
              and (op.dept_code like CONCAT('%',#{params.dept},'%') OR op.dept_name like CONCAT('%',#{params.dept},'%'))
          </if>
          <if test="params.goods != null and params.goods != ''">
              and (opl.goods_code like CONCAT('%',#{params.goods},'%')
              OR opl.goods_erp_code like CONCAT('%',#{params.goods},'%')
              OR opl.goods_name like CONCAT('%',#{params.goods},'%')
              OR opl.goods_model like CONCAT('%',#{params.goods},'%')
              )
          </if>
        <if test="params.whereType !=null and params.whereType != ''">
            <choose>
                <when test="params.whereType == 1 or params.whereType == '1'">
                    and opl.item_stat=1 and op.stat&gt;2
                </when>
                <when test="params.whereType == 2 or params.whereType == '2'">
                    and op.reply_stat=1 and now()&gt;opl.delivery_date
                </when>
                <when test="params.whereType == 3 or params.whereType == '3'">
                    and opl.item_stat=2
                </when>
                <when test="params.whereType == 4 or params.whereType == '4'">
                </when>
                <when test="params.whereType == 5 or params.whereType == '5'">
                    and (datediff(DATE_FORMAT(delivery_date,'%Y-%m-%d'),DATE_FORMAT(order_date,'%Y-%m-%d')) between
                    0 and 3 or
                    (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and
                    jof.order_type=2 and jof.oper_type=2 and jof.order_id=op.id and jof.delete_flag=0)&gt;0)
                    and   (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and
                    jof.order_type=2 and jof.oper_type=2 and jof.order_id=op.id and jof.delete_flag=1)&lt;1
                </when>
                <when test="params.whereType == 6 or params.whereType == '6'">
                    and (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and
                    jof.order_type=2 and jof.oper_type=1 and jof.order_id=op.id and jof.delete_flag=0)&gt;0
                </when>
                <when test="params.whereType == 7 or params.whereType == '7'">
                    and opl.item_stat=4 and fix_num=0
                </when>
                <when test="params.whereType == 8 or params.whereType == '8'">
                    and opl.item_stat=4 and fix_num=0 and now()>opl.reply_date
                </when>
                <when test="params.whereType == 9 or params.whereType == '9'">
                    and opl.item_stat=4 and opl.wait_num&gt;0
                </when>
                <when test="params.whereType == 10 or params.whereType == '10'">
                    and opl.item_stat=4 and opl.wait_num=0
                </when>
                <when test="params.whereType == 11 or params.whereType == '11'">
                    and op.stat &lt;=2
                </when>
                <when test="params.whereType == 12 or params.whereType == '12'">
                    and opl.item_stat=4 and fix_num=0
                </when>
                <!-- 我关注的 >0 就代表是被关注的-->
                <when test="params.whereType == 13 or params.whereType == 13">
                    and x1.isFollow &gt; 0
                </when>
                <!-- 急单 is_busy -->
                <when test="params.whereType == 14 or params.whereType == 14">
                    and opl.is_busy = 1
                </when>
                <!-- 已确认 -->
                <when test="params.whereType == 15 or params.whereType == '15'">
                    and opl.item_stat = 4
                </when>
            </choose>
        </if>
      </where>
    </select>

    <!-- 查询销售订单列表中的气泡数 -->
    <select id="confirmCountSaleMap" parameterType="java.util.HashMap" resultType="java.lang.Long">
        SELECT
          count(*) orderCount
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        <!-- 销售订单列表中根据订单类型>2来查询 订单类型：stat -->
        and op.stat &gt; 2
        <!--
           左连接查询
           oper_type 关注或急单 1为关注
           order_type 订单类型 2为样品订单
           delete_flag 删除标志 0否（No） 1是（Yes）
           这里的orderId与销售订单明细表opl的id进行关联
           isFollow 和 x1 都是别名
       -->
        left join (
            select jof.order_id,
                    sum(
                        case when jof.follow_id= #{params.userId}
                        and jof.oper_type = 1
                        and jof.order_type = 2
                        and jof.delete_flag = 0 then 1 else 0 end
                    ) isFollow
            from order_follow jof
            group by  jof.order_id
        )x1 on x1.order_id = opl.id
        <where>
            <!-- 模糊条件查询 -->
            <if test="params.keyword != null and params.keyword != ''">
                <!--
                  条件如下：↓
                      销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称 tenant_name
                      销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号soure_no、行备注line_remark
                -->
                and (
                op.pur_no like CONCAT('%',#{params.keyword},'%')
                or op.tenant_id like CONCAT('%',#{params.keyword},'%')
                or op.tenant_name like CONCAT('%',#{params.keyword},'%')
                or opl.goods_name like CONCAT('%',#{params.keyword},'%')
                or opl.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                or opl.goods_model like CONCAT('%',#{params.keyword},'%')
                or opl.soure_no like CONCAT('%',#{params.keyword},'%')
                or opl.line_remark like CONCAT('%',#{params.keyword},'%')
                or opl.drawing_no like CONCAT('%',#{params.keyword},'%')
                )
            </if>

            <!-- 根据供应商的id来校验 -->
            <if test="params.vendorId !=null and params.vendorId != ''">
                and op.vendor_id = #{params.vendorId}
            </if>

            <!-- 根据订单表中的订单类型作为条件进行查询，销售方是orderType为2的 采购方是orderType为1的 -->
            <if test="params.orderType !=null and params.orderType != '' and params.orderType !=99">
                and op.order_type = #{params.orderType}
            </if>
            <if test="params.bsart !=null and params.bsart != ''">
                and op.bsart = #{params.bsart}
            </if>
            <if test="params.reserved06 !=null and params.reserved06 != ''">
                and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
            </if>
            <!-- 根据订单日期查询 - 订单日期范围过滤 - sorderDate起始时间 - eorderDate结束时间 -->
            <if test="params.sorderDate != null and params.sorderDate != ''">
                <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.eorderDate != null and params.eorderDate != ''">
                <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>

            <!-- 销售订单列表气泡的动态查询条件 -->
            <if test="params.whereType !=null and params.whereType != '' and params.whereType != 100">
                <choose>
                    <!--新单-->
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and opl.item_stat = 1
                    </when>
                    <!--超时未答交，超时时间从绩效模块取值：答交及时率-->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        <![CDATA[
                            and opl.item_stat=1 and TIMESTAMPDIFF(MINUTE,publish_date,now()) > #{params.replyMinute}
                        ]]>
                    </when>
                    <!--答交异常-->
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and opl.item_stat=2
                    </when>
                    <!--已确认-->
                    <when test="params.whereType == 4 or params.whereType == '4'">
                        and opl.item_stat = 4
                    </when>
                    <!--全部-->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                    </when>
                    <!-- 急单 -->
                    <when test="params.whereType == 6 or params.whereType == 6">
                        and opl.is_busy = 1
                    </when>
                    <!-- 我关注的 >0 就代表是被关注的-->
                    <when test="params.whereType == 7 or params.whereType == 7">
                        and x1.isFollow &gt; 0
                    </when>

                </choose>
            </if>
        </where>
    </select>


    <!-- 查询销售订单进度列表中的气泡数 -->
    <select id="progressCountSaleMap" parameterType="java.util.HashMap" resultType="java.lang.Long">
        SELECT
          count(*) orderCount
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        <!-- 查询销售订单进度列表根据订单明细表中的行状态（item_）来  and op.stat &gt;2 -->
        and opl.item_stat = 4
        <!--
            oper_type 关注或急单 1为关注
            order_type 订单类型 1为普通订单
            delete_flag 删除标志 0否（No） 1是（Yes）
            这里的orderId与销售订单明细表opl的id进行关联
            isFollow 和 x1 都是别名
        -->
        left join (
            select jof.order_id,
                    sum(
                          case when jof.follow_id=#{params.userId}
                          and jof.oper_type = 1
                          and jof.order_type = 2
                          and jof.delete_flag = 0 then 1 else 0 end
                    ) isFollow
            from  order_follow jof
            group by jof.order_id
        )x1 on x1.order_id = opl.id
        <where>
            <!-- 模糊条件查询 -->
            <if test="params.keyword != null and params.keyword != ''">
                <!--
                  条件如下：↓
                      销售订单表：销售订单号 pur_no、采购方id tenant_id、采购方企业名称 tenant_name
                      销售订单明细表：品号 goods_erp_code、品名 goods_name、物料描述 goods_model、来源单号soure_no、行备注line_remark
                -->
                and (
                op.pur_no like CONCAT('%',#{params.keyword},'%')
                or op.tenant_id like CONCAT('%',#{params.keyword},'%')
                or op.tenant_name like CONCAT('%',#{params.keyword},'%')
                or opl.goods_name like CONCAT('%',#{params.keyword},'%')
                or opl.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                or opl.goods_model like CONCAT('%',#{params.keyword},'%')
                or opl.soure_no like CONCAT('%',#{params.keyword},'%')
                or opl.line_remark like CONCAT('%',#{params.keyword},'%')
                or opl.drawing_no like CONCAT('%',#{params.keyword},'%')
                )
            </if>

            <!-- 根据订单类型查询 -->
            <if test="params.orderType != null and params.orderType  != '' and params.orderType != 99">
                and op.order_type = #{params.orderType}
            </if>
            <if test="params.bsart !=null and params.bsart != ''">
                and op.bsart = #{params.bsart}
            </if>

            <!-- 根据采购方的id来查询数据 -->
            <if test="params.vendorId !=null and params.vendorId != ''">
                and op.vendor_id = #{params.vendorId}
            </if>
            <if test="params.reserved06 !=null and params.reserved06 != ''">
                and op.reserved06 like CONCAT('%',#{params.reserved06},'%')
            </if>
            <!-- 根据订单时间查询 查询一个时间段区间的订单信息 如：查询2021/7/10 - 2021/7/23的订单 -->
            <if test="params.sorderDate != null and params.sorderDate != ''">
                <![CDATA[
                    and op.order_date >=STR_TO_DATE(#{params.sorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>
            <if test="params.eorderDate != null and params.eorderDate != ''">
                <![CDATA[
                    and op.order_date <=STR_TO_DATE(#{params.eorderDate},'%Y-%m-%d %H:%i:%s')
                ]]>
            </if>

            <!-- 动态的气泡过滤条件 -->
            <if test="params.whereType !=null and params.whereType != ''">
                <choose>
                    <!--未送货-->
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and opl.item_stat=4 and fix_num=0
                    </when>
                    <!--超时未送货-->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        and opl.item_stat=4 and fix_num=0 and now() &gt; opl.reply_date
                    </when>
                    <!--未完成-->
                    <when test="params.whereType == 3 or params.whereType == '3'">
--                         and opl.item_stat=4 and opl.wait_num&gt;0
                        and opl.order_num&lt;&gt;(opl.erp_master_num-opl.erp_reject_num)
                    </when>
                    <!--已完成-->
                    <when test="params.whereType == 4 or params.whereType == '4'">
--                         and opl.item_stat=4 and opl.wait_num=0
                        and opl.order_num=(opl.erp_master_num-opl.erp_reject_num)
                    </when>
                    <!--全部-->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                    </when>
                    <!-- 急单 -->
                    <when test="params.whereType == 6 or params.whereType == 6">
                        and opl.is_busy = 1
                    </when>
                    <!-- 我关注的 >0 就代表是被关注的-->
                    <when test="params.whereType == 7 or params.whereType == 7">
                        and x1.isFollow &gt; 0
                    </when>

                </choose>
            </if>
        </where>
    </select>

    <select id="findOrderBySaleId" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            opl.werks,opl.order_price_uom,opl.class_code goods_class_code,opl.goods_class_name,
            op.bsart,
            op.id,op.pur_no,op.vendor_id,op.vendor_code,op.vendor_name,op.dept_name,op.order_date,op.source_no,
            op.sync_date,op.order_type,op.stat,op.reply_stat,op.change_count,op.remark,op.purchasing_group,op.tenant_id,op.tenant_name,
            op.creater,op.modifier,op.create_date,op.create_id,op.modify_date,op.modifi_id,
            op.delete_flag,op.create_id,op.creater,op.create_date,op.modifi_id,op.modifier,
            op.modify_date, opl.id as line_id,opl.pur_id,opl.goods_id,opl.goods_erp_code,opl.goods_code,opl.goods_name,opl.price_uom,
            opl.goods_model,opl.uom_id,opl.uom_code,opl.uom_name,opl.rate_id,opl.rate_name,opl.rate_val,opl.currency_id,opl.currency_name,opl.taxes_type,opl.invoice_type,
            opl.gst_price,opl.tax_price,opl.barcode_type,opl.warehouse_id,opl.warehouse_code,opl.warehouse_name,opl.soure_no,opl.change_count
            as line_change_count,opl.item_stat,opl.delivery_date,opl.sign,opl.line_remark,
            opl.reply_date,opl.purchase_remark,opl.vendor_remark, opl.is_close,opl.is_return_po,
            opl.make_num,opl.order_num line_order_sum,opl.fix_num,opl.receive_num,opl.refund_num,IFNULL(opl.erp_master_num,0) erp_master_num,IFNULL(opl.erp_reject_num,0) erp_reject_num,
            opl.purchase_confirm,opl.vendor_confirm,opl.purchase_doc_url ,opl.vendor_doc_url,
            opl.wait_num,opl.create_date,opl.is_main,opl.delivery_type,opl.main_item_id,opl.big_pack_standard_num,opl.small_pack_standard_num,opl.big_pack_label_num,opl.small_pack_label_num
            ,opl.big_pack_mantissa,opl.small_pack_mantissa,
            IFNULL(opl.order_num,0)-IFNULL(opl.make_num,0) as can_Make_Num,
            opl.pur_employee_name,opl.sale_employee_name,opl.goods_class_name,opl.delivery_stat,opl.delete_flag as
            line_delete_flag,opl.create_id as line_create_id,opl.creater as line_creater
            ,opl.create_date as line_create_date,opl.modifi_id as line_modifi_id,
            opl.modifier as line_modifier,opl.modify_date as line_modify_date,opl.seq,opl.tax_Amount,opl.gst_Amount,opl.main_item_code,ifnull(opl.drawing_no,'-') as drawing_no
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        where opl.id = #{saleItemId}
    </select>

    <!--根据物料编码+供应商编码查询出最后一条关于该物料的订单信息（最新的一条采购订单）-->
    <select id="findLastOneByGoodsErpCode" parameterType="java.util.Map" resultType="java.util.Map">
        select
            opl.werks,opl.order_price_uom,opl.class_code goods_class_code,opl.goods_class_name,
            op.bsart,
            op.id,op.pur_no,op.vendor_id,op.vendor_code,op.vendor_name,op.dept_name,op.order_date,op.source_no,
            op.sync_date,op.order_type,op.stat,op.reply_stat,op.change_count,op.remark,op.purchasing_group,op.tenant_id,op.tenant_name,
            op.creater,op.modifier,op.create_date,op.create_id,op.modify_date,op.modifi_id,
            op.delete_flag,op.create_id,op.creater,op.create_date,op.modifi_id,op.modifier,
            op.modify_date, opl.id as line_id,opl.pur_id,opl.goods_id,opl.goods_erp_code,opl.goods_code,opl.goods_name,opl.price_uom,
            opl.goods_model,opl.uom_id,opl.uom_code,opl.uom_name,opl.rate_id,opl.rate_name,opl.rate_val,opl.currency_id,opl.currency_name,opl.taxes_type,opl.invoice_type,
            opl.gst_price,opl.tax_price,opl.barcode_type,opl.warehouse_id,opl.warehouse_code,opl.warehouse_name,opl.soure_no,opl.change_count
            as line_change_count,opl.item_stat,opl.delivery_date,opl.sign,opl.line_remark,
            opl.reply_date,opl.purchase_remark,opl.vendor_remark, opl.is_close, opl.is_return_po,
            opl.make_num,opl.order_num line_order_sum,opl.fix_num,opl.receive_num,opl.refund_num,opl.erp_master_num,opl.erp_reject_num,
            opl.purchase_confirm,opl.vendor_confirm,opl.purchase_doc_url ,opl.vendor_doc_url,
            opl.wait_num,opl.create_date,opl.is_main,opl.delivery_type,opl.main_item_id,opl.big_pack_standard_num,opl.small_pack_standard_num,opl.big_pack_label_num,opl.small_pack_label_num
            ,opl.big_pack_mantissa,opl.small_pack_mantissa,
            IFNULL(opl.order_num,0)-IFNULL(opl.make_num,0) as can_Make_Num,
            opl.pur_employee_name,opl.sale_employee_name,opl.goods_class_name,opl.delivery_stat,opl.delete_flag as
            line_delete_flag,opl.create_id as line_create_id,opl.creater as line_creater
            ,opl.create_date as line_create_date,opl.modifi_id as line_modifi_id,
            opl.modifier as line_modifier,opl.modify_date as line_modify_date,opl.seq,opl.tax_Amount,opl.gst_Amount,opl.main_item_code,ifnull(opl.drawing_no,'-') as drawing_no
        from order_pur op inner join order_pur_item opl
        on op.id = opl.pur_id and op.delete_flag=0 and opl.delete_flag=0
        where opl.goods_erp_code = #{params.goodsErpCode}
        and op.vendor_code = #{params.vendorCode}
        order by op.id desc limit 1
    </select>

    <!-- 根据采购订单中的purId 查询供应商对应的采购员用户信息 -->
    <select id="getUserById" parameterType="java.util.Map" resultType="java.util.Map">
        select
          id,tenant_id,dept_id,user_code,user_name,user_mobile,user_email
        from
          sys_user
        where
          tenant_id = #{params.tenantId}
        <if test="params.purId">
            and id = #{params.purId}
        </if>
    </select>
    <select id="queryBySumNum" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(match_num),0)
             FROM dm_delivery_plan_item dpi
              WHERE dpi.sale_item_id=#{id}
    </select>
</mapper>
