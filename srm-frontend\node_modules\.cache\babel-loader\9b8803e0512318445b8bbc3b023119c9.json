{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\vendor\\index.vue", "mtime": 1754027118419}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _dianUiVue = require(\"@dian/dian-ui-vue\");\nvar _Form = _interopRequireDefault(require(\"@/views/order/produce/vendor/Form\"));\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _sale = require(\"@/api/order/sale\");\nvar _dian = _interopRequireDefault(require(\"@/utils/dian\"));\nvar _codePrintTemplate = _interopRequireDefault(require(\"./codePrintTemplate\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  //加载底层公有组件\n  mixins: [_dianUiVue.dFlowMixin],\n  name: \"order-progress-vendor\",\n  components: {\n    Form: _Form.default,\n    CodePrintTemplate: _codePrintTemplate.default\n  },\n  data: function data() {\n    return {\n      bsartTypeOptions: _store.default.getters.commonEnums['order.BsartTypeEnum'],\n      // 订单类型\n      queryParam: {\n        page: 1,\n        limit: 20,\n        keyword: '',\n        // 订单号/物料编码/物料名称/物料描述\n        orderDate: '',\n        // 订单日期\n        dept: '',\n        orderNo: '',\n        goods: '',\n        bsart: '' // 采购类型\n      },\n\n      formVisible: false,\n      listLoading: false,\n      btnLoading: false,\n      templateVisible: false,\n      list: [],\n      total: 0\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: {\n    initData: function initData() {\n      var _this = this;\n      this.listLoading = true;\n      if (this.queryParam.orderDate.length !== 0) {\n        var startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');\n        var endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');\n        this.queryParam.orderDate = startDate + \" 至 \" + endDate;\n      }\n      var subCompanyInfoData = _dian.default.storageGet('subCompanyInfo');\n      if (subCompanyInfoData) {\n        this.queryParam.deptId = subCompanyInfoData.id;\n      }\n      (0, _sale.getVendorSaleOrderProgressList)(this.queryParam).then(function (res) {\n        _this.total = res.page.totalCount;\n        _this.list = res.page.list;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    // 新增|编辑 项目报备\n    addEditOrderHandle: function addEditOrderHandle(id) {\n      var _this2 = this;\n      this.formVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.form.init(id);\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n      //获取所有选中项数组的长度\n      this.selectedNum = selection.length;\n    },\n    // 导出\n    exportHandle: function exportHandle() {\n      // 当导出为全部时，赋予总数为最大值\n      this.queryParam.total = this.total;\n      this.$refs.export.init('/api/order/sale/saleProgressExport', '订单执行进度表', this.queryParam);\n    },\n    // 搜索方法，并返回到第一页\n    search: function search() {\n      this.queryParam.page = 1;\n      this.initData();\n    },\n    // 重置方法\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.search();\n    },\n    // Form表单关闭时回调方法\n    closeForm: function closeForm() {\n      this.formVisible = false;\n      this.initData();\n    },\n    // 打印条码\n    printBarCode: function printBarCode(data) {\n      var _this3 = this;\n      this.templateVisible = true;\n      var params = {\n        id: null,\n        tenantPId: 0,\n        templateName: '',\n        sceneId: null,\n        sceneCode: '',\n        sceneName: '',\n        remark: '',\n        printJson: '',\n        goodsName: data.goodsName,\n        goodsCode: data.goodsErpCode,\n        vendorName: data.vendorName,\n        vendorCode: data.vendorCode,\n        devSumNum: 0,\n        // 总送货数\n        PackagingNum: 0,\n        // 每箱数量\n        numberOfBoxes: 0,\n        // 箱数\n        countless: 0,\n        // 尾数\n        prodDate: null,\n        // 生产日期\n        warrantyPeriod: '',\n        // 质保天数\n        warrantyDate: '',\n        // 质保日期\n        prodBatchNo: '' // 批次号\n      };\n\n      this.$nextTick(function () {\n        _this3.$refs.PrintTemplate.goodsCode = data.goodsCode;\n        _this3.$refs.PrintTemplate.isBatchPrint = false;\n        _this3.$refs.PrintTemplate.printData = params;\n        _this3.$refs.PrintTemplate.dataForm = params;\n        _this3.$refs.PrintTemplate.init(\"deliveryNotePrinting\");\n      });\n    }\n  }\n};\nexports.default = _default;", null]}