{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue", "mtime": 1754011601066}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es7.array.includes\");\nrequire(\"core-js/modules/es6.regexp.search\");\nrequire(\"core-js/modules/web.dom.iterable\");\nvar _objectSpread2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _sampleDemand = require(\"@/api/base/sampleDemand\");\nvar _form = _interopRequireDefault(require(\"./form\"));\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _vendor = _interopRequireDefault(require(\"@/views/popup/base/vendor/vendor\"));\nvar _userProp = _interopRequireDefault(require(\"@/views/popup/sys/user/userProp\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  components: {\n    Form: _form.default,\n    VendorProp: _vendor.default,\n    UserProp: _userProp.default\n  },\n  name: 'base-sampleDemand-tenant',\n  data: function data() {\n    return {\n      list: [],\n      total: 0,\n      showAll: false,\n      queryParam: {\n        //查询条件\n        demandNo: '',\n        //需求申请单号\n        applicant: '',\n        //申请人\n        isNeedUpFile: null,\n        //明细表检验状态\n        dept: '',\n        // 采购组织\n        applyDate: '',\n        // 申请日期\n        startDate: '',\n        // 开始时间\n        endDate: '',\n        // 结束时间\n        demandDate: '',\n        // 需求日期\n        demandDateStart: '',\n        // 需求开始日期\n        demandDateEnd: '',\n        // 需求结束日期\n        selectType: '3',\n        // 默认查询显示全部的数据\n        demandClassType: '2',\n        // 默认查询显示采购打样单据\n        caseStat: '1',\n        // 默认查询待分配的数据\n        page: 1,\n        limit: 20\n      },\n      detailVisible: false,\n      qualityVisible: false,\n      listLoading: true,\n      btnLoading: false,\n      // 按钮加载状态\n      selectedDatas: [],\n      sampleDates: [],\n      demandDates: [],\n      // 需求日期选择器\n      applyDates: [],\n      // 申请日期选择器\n      sampleItemStatOptions: _store.default.getters.commonEnums['base.SampleItemEnums'],\n      // 明细行检验状态\n      sampleStatOptions: _store.default.getters.commonEnums['base.SampleEnums'],\n      // 需求状态\n      validOps: _store.default.getters.commonEnums['comm.ValidEnum'],\n      // 检验状态\n      demandTypeOption: _store.default.getters.commonEnums['base.DemandClassTypeEnum'],\n      // 单据类型\n      selectTypeOptions: [\n      // 查询类型\n      {\n        key: '1',\n        value: '未完成'\n      }, {\n        key: '2',\n        value: '已完成'\n      }, {\n        key: '3',\n        value: '全部'\n      }],\n      caseStatOptions: [\n      // 需求状态\n      {\n        key: '',\n        value: '全部'\n      }, {\n        key: '1',\n        value: '待分配'\n      }, {\n        key: '2',\n        value: '已分配'\n      }, {\n        key: '3',\n        value: '已拒绝'\n      }, {\n        key: '4',\n        value: '退回'\n      }, {\n        key: '5',\n        value: '已结案'\n      }]\n    };\n  },\n  created: function created() {\n    this.handleRouteQuery();\n    this.initData();\n  },\n  watch: {\n    // 监听路由变化\n    '$route': function $route(to) {\n      if (to.path === '/base/sampleDemand/tenant' && to.query.id) {\n        this.handleRouteQuery();\n      }\n    }\n  },\n  filters: {\n    date: function date(time) {\n      if (!time) {\n        return '';\n      }\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = date.getMonth() + 1;\n      var day = date.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    }\n  },\n  methods: {\n    handleRouteQuery: function handleRouteQuery() {\n      var _this = this;\n      var query = this.$route.query;\n      if (query.detailVisible && query.id) {\n        if (this.detailVisible) {\n          this.detailVisible = false;\n          this.$nextTick(function () {\n            _this.detailVisible = true;\n            _this.$nextTick(function () {\n              _this.$refs.detail.init(query.id);\n            });\n          });\n        } else {\n          this.detailVisible = true;\n          this.$nextTick(function () {\n            _this.$refs.detail.init(query.id);\n          });\n        }\n      }\n    },\n    // 页面初始化加载列表数据\n    initData: function initData() {\n      var _this2 = this;\n      this.listLoading = true;\n      var query = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, this.queryParam), {}, {\n        includeVendors: true // 请求包含供应商信息\n      });\n\n      (0, _sampleDemand.getSampleDemandList)(query).then(function (res) {\n        _this2.total = res.data.totalCount;\n        _this2.list = res.data.list;\n        // 处理供应商信息\n        if (_this2.list && _this2.list.length > 0) {\n          _this2.list.forEach(function (item) {\n            if (!item.sampleDemandVendorEntityList) {\n              item.sampleDemandVendorEntityList = [];\n            }\n          });\n        }\n        _this2.listLoading = false;\n      }).catch(function () {\n        _this2.listLoading = false;\n      });\n    },\n    //打开新增/修改弹窗页面\n    addOrUpdateHandle: function addOrUpdateHandle(id) {\n      var _this3 = this;\n      this.detailVisible = true;\n      this.$nextTick(function () {\n        _this3.$refs.detail.init(id);\n      });\n    },\n    //审核\n    handleCheck: function handleCheck(id) {},\n    search: function search() {\n      this.queryParam.page = 1;\n\n      // 处理申请日期\n      if (this.applyDates.length === 2) {\n        try {\n          var startDate = this.$dian.dateFormat(this.applyDates[0], 'YYYY-MM-DD');\n          var endDate = this.$dian.dateFormat(this.applyDates[1], 'YYYY-MM-DD');\n          this.queryParam.startDate = startDate;\n          this.queryParam.endDate = endDate;\n        } catch (error) {\n          this.queryParam.startDate = '';\n          this.queryParam.endDate = '';\n        }\n      } else {\n        this.queryParam.startDate = '';\n        this.queryParam.endDate = '';\n      }\n\n      // 处理需求日期\n      if (this.demandDates.length === 2) {\n        try {\n          var demandDateStart = this.$dian.dateFormat(this.demandDates[0], 'YYYY-MM-DD');\n          var demandDateEnd = this.$dian.dateFormat(this.demandDates[1], 'YYYY-MM-DD');\n          this.queryParam.demandDateStart = demandDateStart;\n          this.queryParam.demandDateEnd = demandDateEnd;\n        } catch (error) {\n          this.queryParam.demandDateStart = '';\n          this.queryParam.demandDateEnd = '';\n        }\n      } else {\n        this.queryParam.demandDateStart = '';\n        this.queryParam.demandDateEnd = '';\n      }\n      this.initData();\n    },\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.demandDates = [];\n      this.applyDates = [];\n      this.initData();\n    },\n    //删除\n    handleDel: function handleDel(id) {\n      var _this4 = this;\n      this.$confirm(this.$t('common.delTip'), this.$t('common.tipTitle'), {\n        type: 'warning'\n      }).then(function () {\n        (0, _sampleDemand.delSampleDemand)(id).then(function (res) {\n          _this4.$message({\n            type: 'success',\n            message: '删除成功',\n            duration: 1500,\n            onClose: function onClose() {\n              _this4.search();\n            }\n          });\n        });\n      }).catch(function () {});\n    },\n    callRefreshList: function callRefreshList() {\n      this.detailVisible = false;\n      this.qualityVisible = false;\n      this.search();\n    },\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n    },\n    quality: function quality(row) {\n      var _this5 = this;\n      this.qualityVisible = true;\n      var isAdd = false;\n      if (row.ifInspection > 0) {\n        isAdd = true;\n      }\n      this.$nextTick(function () {\n        _this5.$refs.quality.init(row.id, isAdd);\n      });\n    },\n    // 打开供应商选择弹窗\n    openVendorDialog: function openVendorDialog() {\n      var _this6 = this;\n      if (this.selectedDatas.some(function (item) {\n        return item.caseStat == 4;\n      })) {\n        this.$message.error('存在已退回的物料明细记录，不允许添加供应商');\n        return;\n      }\n      if (this.selectedDatas.length === 0) {\n        this.$message.error('请先选择物料');\n        return;\n      }\n\n      // 获取组织ID，使用第一个选中项的组织ID\n      var orgId = this.selectedDatas[0].orgId;\n      if (!orgId) {\n        this.$message.error('采购组织信息为空');\n        return;\n      }\n      var params = {\n        deptIds: orgId\n      };\n      this.$nextTick(function () {\n        _this6.$refs.vendor.init(params);\n      });\n    },\n    // 处理供应商选择结果\n    vendorSelect: function vendorSelect(vendorData) {\n      if (!vendorData || vendorData.length === 0) {\n        this.$message.error('未选择任何供应商');\n        return;\n      }\n      if (this.selectedDatas.length === 0) {\n        this.$message.error('未选择任何物料');\n        return;\n      }\n\n      // 处理供应商数据\n      this.processVendorData(vendorData, this.selectedDatas);\n    },\n    // 批量生成送样通知单\n    batchGenerateSampleNotice: function batchGenerateSampleNotice(sampleIds) {\n      var _this7 = this;\n      this.btnLoading = true;\n      console.log(\"批量生成送样通知单 =======> \", sampleIds);\n      (0, _sampleDemand.batchIssuedSample)(sampleIds).then(function (res) {\n        _this7.$message({\n          message: \"送样通知单生成成功\",\n          type: 'success',\n          duration: 1500,\n          onClose: function onClose() {\n            _this7.btnLoading = false;\n            _this7.search(); // 刷新列表\n          }\n        });\n      }).catch(function (err) {\n        _this7.btnLoading = false;\n        _this7.$message.error('生成送样通知单失败: ' + (err.message || '未知错误'));\n      });\n    },\n    // 处理供应商数据\n    processVendorData: function processVendorData(vendorData, materials) {\n      var _this8 = this;\n      // 存储需要更新的数据\n      var updateData = [];\n\n      // 遍历所有选中的物料\n      var _loop = function _loop() {\n        var material = materials[i];\n\n        // 确保供应商列表存在\n        if (!material.sampleDemandVendorEntityList) {\n          material.sampleDemandVendorEntityList = [];\n        }\n        // 初始化物料明细列表\n        if (!material.sampleDemandItemEntityList) {\n          material.sampleDemandItemEntityList = [];\n        }\n\n        // 为每个物料添加选中的供应商\n        var _loop2 = function _loop2() {\n          var vendor = vendorData[j];\n\n          // 检查是否已存在相同的供应商\n          var isExist = material.sampleDemandVendorEntityList.some(function (item) {\n            return item.goodsId === material.goodsId && item.vendorId === vendor.soureId;\n          });\n\n          // 如果不存在，则填充供应商数据\n          if (!isExist) {\n            var vendorItem = {\n              demandId: material.demandId,\n              demandItemId: material.id,\n              goodsId: material.goodsId,\n              goodsErpCode: material.goodsErpCode,\n              goodsName: material.goodsName,\n              vendorId: vendor.soureId,\n              vendorCode: vendor.vendorErpCode,\n              vendorName: vendor.vendorFullName,\n              isAssigned: 0,\n              isReturn: 0,\n              // returnRemark: '',\n              returnCause: '',\n              caseDate: null,\n              caseStat: null,\n              isValid: 1,\n              deleteFlag: 0\n            };\n\n            // 填充物料明细\n            var materialItem = {\n              id: material.itemId,\n              demandId: material.demandId,\n              goodsId: material.goodsId,\n              goodsErpCode: material.goodsErpCode,\n              goodsName: material.goodsName,\n              goodsModel: material.goodsModel,\n              vendorId: material.vendorId,\n              vendorCode: material.vendorCode,\n              vendorName: material.vendorName,\n              demandDate: material.demandDate,\n              demandQty: material.demandQty,\n              saleDeptId: material.saleDeptId,\n              saleDeptCode: material.saleDeptCode,\n              saleDeptName: material.saleDeptName,\n              model: material.model,\n              purpose: material.purpose,\n              uomId: material.uomId,\n              uomCode: material.uomCode,\n              uomName: material.uomName,\n              caseDate: material.caseDate,\n              sourceItemId: material.sourceItemId,\n              caseStat: material.caseStat,\n              isValid: material.itemIsValid,\n              deleteFlag: material.itemDeleteFlag,\n              remark: material.item_remark,\n              createId: material.itemCreateId,\n              creater: material.itemCreater,\n              createDate: material.itemCreateDate,\n              modifiId: material.itemModifiId,\n              modifier: material.itemModifier,\n              modifyDate: material.itemModifyDate\n            };\n            material.sampleDemandVendorEntityList.push(vendorItem);\n            material.sampleDemandItemEntityList.push(materialItem);\n\n            // 添加到需要更新的数据中\n            if (!updateData.includes(material)) {\n              updateData.push(material);\n            }\n          }\n        };\n        for (var j = 0; j < vendorData.length; j++) {\n          _loop2();\n        }\n      };\n      for (var i = 0; i < materials.length; i++) {\n        _loop();\n      }\n\n      // 如果有数据需要更新\n      if (updateData.length > 0) {\n        this.$confirm('是否保存供应商数据并生成送样通知单?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function () {\n          _this8.saveSampleVendors(updateData);\n        }).catch(function () {\n          _this8.$message({\n            type: 'info',\n            message: '已取消操作'\n          });\n        });\n      } else {\n        this.$message.error('没有新的供应商数据需要保存');\n      }\n    },\n    saveSampleVendors: function saveSampleVendors(materials) {\n      var _this9 = this;\n      this.btnLoading = true;\n      var updatePromises = materials.map(function (material) {\n        console.log(\"保存参数为=====================>\", JSON.stringify(material, null, 2));\n        return (0, _sampleDemand.updateSampleDemand)(material);\n      });\n      Promise.all(updatePromises).then(function () {\n        _this9.$message({\n          message: \"供应商数据保存成功\",\n          type: 'success',\n          duration: 1500\n        });\n\n        // 保存成功后，调用批量生成送样通知单的方法\n        var sampleIds = materials.map(function (item) {\n          return item.id;\n        });\n        _this9.batchGenerateSampleNotice(sampleIds);\n      }).catch(function (err) {\n        _this9.btnLoading = false;\n        _this9.$message.error('保存失败: ' + (err.message || '未知错误'));\n      });\n    },\n    //批量退回PLM\n    batchReturnSample: function batchReturnSample() {\n      var _this10 = this;\n      // 检查是否有选中的数据\n      if (this.selectedDatas.length === 0) {\n        this.$message.error('请至少选择一条需要退回的物料明细记录');\n        return;\n      }\n      // 检查选中的物料是否存在sourceItemId\n      if (this.selectedDatas.some(function (item) {\n        return !item.sourceItemId;\n      })) {\n        // this.$message.error(\"存在未关联的物料明细行需求，请先关联PLM物料明细行\");\n        this.$message.error(\"存在未关联PLM的物料，只允许退回从PLM下发的物料\");\n        return;\n      }\n      // 检查是否已退回\n      if (this.selectedDatas.some(function (item) {\n        return item.caseStat == 4;\n      })) {\n        this.$message.error('存在已退回的物料明细记录，请勿重复退回');\n        return;\n      }\n      this.$prompt('请输入退回原因', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputType: 'textarea',\n        inputPlaceholder: '请输入退回原因'\n      }).then(function (_ref) {\n        var value = _ref.value;\n        if (!value) {\n          _this10.$message.error('退回原因不能为空');\n          return;\n        }\n        // 构造请求参数\n        _this10.btnLoading = true;\n        var ids = _this10.selectedDatas.map(function (item) {\n          return item.itemId;\n        }); // 物料明细ID\n        var params = {\n          ids: ids,\n          // 物料明细ID数组\n          returnCause: value // 退回原因\n        };\n\n        (0, _sampleDemand.returnSample)(params).then(function (res) {\n          _this10.$message({\n            message: \"退回成功\",\n            type: 'success',\n            duration: 1500,\n            onClose: function onClose() {\n              _this10.btnLoading = false;\n              _this10.search(); // 刷新列表\n            }\n          });\n        }).catch(function (err) {\n          _this10.btnLoading = false;\n          _this10.$message.error('退回失败: ' + (err.message || '未知错误'));\n        });\n      }).catch(function () {\n        _this10.$message({\n          type: 'info',\n          message: '已取消退回'\n        });\n      });\n    },\n    //批量本地结案\n    batchLocalClosed: function batchLocalClosed() {\n      var _this11 = this;\n      // 检查是否有选中的数据\n      if (this.selectedDatas.length === 0) {\n        this.$message.error('请至少选择一条需要结案的物料明细记录');\n        return;\n      }\n      // 检查选中的物料是否存在sourceItemId\n      if (this.selectedDatas.some(function (item) {\n        return item.sourceItemId;\n      })) {\n        this.$message.error(\"存在已关联PLM的物料，不允许本地结案\");\n        return;\n      }\n      // 检查是否已结案\n      if (this.selectedDatas.some(function (item) {\n        return item.caseStat == 5;\n      })) {\n        this.$message.error('存在已结案的物料明细记录，请勿重复结案');\n        return;\n      }\n      this.btnLoading = true;\n      var ids = this.selectedDatas.map(function (item) {\n        return item.itemId;\n      }); // 物料明细ID\n      this.$confirm(\"\\u786E\\u8BA4\\u7ED3\\u6848\\u9009\\u4E2D\\u7684\".concat(this.selectedDatas.length, \"\\u6761\\u8BB0\\u5F55\\uFF1F\"), '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        (0, _sampleDemand.localClosed)(ids).then(function (res) {\n          _this11.$message({\n            message: \"结案成功\",\n            type: 'success',\n            duration: 1500,\n            onClose: function onClose() {\n              _this11.btnLoading = false;\n              _this11.search(); // 刷新列表\n            }\n          });\n        }).catch(function (err) {\n          _this11.btnLoading = false;\n          _this11.$message.error('结案失败: ' + (err.message || '未知错误'));\n        });\n      }).catch(function () {\n        _this11.$message({\n          type: 'info',\n          message: '已取消结案'\n        });\n      });\n    },\n    // 打开采购员选择弹窗\n    openBuyerDialog: function openBuyerDialog() {\n      var _this12 = this;\n      if (this.selectedDatas.length === 0) {\n        this.$message.error('请至少选择一条物料明细记录');\n        return;\n      }\n\n      // 检查是否已退回\n      if (this.selectedDatas.some(function (item) {\n        return item.caseStat == 4;\n      })) {\n        this.$message.error('存在已退回的物料明细记录，不允许更换采购员');\n        return;\n      }\n      this.$nextTick(function () {\n        _this12.$refs.userProp.init();\n      });\n    },\n    // 处理用户选择结果\n    userSelect: function userSelect(userData) {\n      var _this13 = this;\n      if (!userData || userData.length === 0) {\n        this.$message.error('未选择采购员');\n        return;\n      }\n      var selectedUser = userData[0];\n      console.log('选择的采购员:', selectedUser);\n\n      // 弹出确认框\n      this.$confirm(\"\\u786E\\u8BA4\\u5C06\\u9009\\u4E2D\\u7684\".concat(this.selectedDatas.length, \"\\u6761\\u8BB0\\u5F55\\u7684\\u91C7\\u8D2D\\u5458\\u66F4\\u6362\\u4E3A \").concat(selectedUser.userName, \"?\"), '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this13.updateBuyer(selectedUser);\n      }).catch(function () {\n        _this13.$message({\n          type: 'info',\n          message: '已取消更换采购员'\n        });\n      });\n    },\n    // 更新采购员信息\n    updateBuyer: function updateBuyer(selectedUser) {\n      var _this14 = this;\n      this.btnLoading = true;\n\n      // 获取选中记录的ID列表\n      var ids = this.selectedDatas.map(function (item) {\n        return item.itemId;\n      }); // 物料明细行ID\n\n      // 构造请求参数\n      var params = {\n        ids: ids,\n        purId: selectedUser.id,\n        purCode: selectedUser.userCode,\n        purName: selectedUser.userName\n      };\n      (0, _sampleDemand.batchUpdateBuyer)(params).then(function (res) {\n        _this14.$message({\n          message: \"采购员更新成功\",\n          type: 'success',\n          duration: 1500,\n          onClose: function onClose() {\n            _this14.btnLoading = false;\n            _this14.search(); // 刷新列表\n          }\n        });\n      }).catch(function (err) {\n        _this14.btnLoading = false;\n        _this14.$message.error('采购员更新失败: ' + (err.message || '未知错误'));\n      });\n    }\n  }\n};\nexports.default = _default;", null]}