[INFO ] [2025-08-01 09:06:42.354] - select '1' from dual
[INFO ] [2025-08-01 09:06:42.579] - select '1' from dual
[INFO ] [2025-08-01 09:06:42.729] - select '1' from dual
[INFO ] [2025-08-01 09:06:42.881] - select '1' from dual
[INFO ] [2025-08-01 09:06:43.037] - select '1' from dual
[INFO ] [2025-08-01 09:09:29.436] - select '1' from dual
[INFO ] [2025-08-01 09:09:30.514] - SELECT id, tenant_p_id, tenant_id, enquiry_code, enquiry_name, enquiry_explain, bid_plan_id, bid_plan_code, enquiry_rule, dept_name, tenant_name, remark, enquiry_type, goods_type, enquiry_mode, enquiry_stat, enquiry_way, ask_date, complete_date, order_date, is_urgent, delete_flag, wf_id, wf_status, version_num, dep_name, pur_org_name, info_type, werks, plans, allow_bid_count, rate_code, bstae, auto_source, tenant_sees_rank_and_price, vendor_sees_rank_and_price, vendor_sees_final_rank_price, is_part_update, enquiry_round, expand_remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM im_enquiry WHERE (enquiry_mode = 3 AND enquiry_stat = 2 AND ask_date > '2025-08-01 09:09:25')
[INFO ] [2025-08-01 09:09:30.756] - SELECT id, tenant_p_id, tenant_id, enquiry_code, enquiry_name, enquiry_explain, bid_plan_id, bid_plan_code, enquiry_rule, dept_name, tenant_name, remark, enquiry_type, goods_type, enquiry_mode, enquiry_stat, enquiry_way, ask_date, complete_date, order_date, is_urgent, delete_flag, wf_id, wf_status, version_num, dep_name, pur_org_name, info_type, werks, plans, allow_bid_count, rate_code, bstae, auto_source, tenant_sees_rank_and_price, vendor_sees_rank_and_price, vendor_sees_final_rank_price, is_part_update, enquiry_round, expand_remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM im_enquiry WHERE (enquiry_mode = 3 AND (enquiry_stat = 2 OR enquiry_stat = 3) AND complete_date > '2025-08-01 09:09:30')
[INFO ] [2025-08-01 09:09:34.888] - /* ping */ SELECT 1
[INFO ] [2025-08-01 09:16:19.252] - <0><1951089566670331904> select '1' from dual
[INFO ] [2025-08-01 09:16:19.355] - <0><1951089566670331904> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-01 09:16:19.540] - <0><1951089566670331904> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:16:19.786] - <0><1951089566670331904> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:16:21.897] - <0><1951089587641851904> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:16:21.945] - <0><1951089587641851904> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:16:22.763] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:16:22.788] - <0><1951089590154240000> select '1' from dual
[INFO ] [2025-08-01 09:16:22.788] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-01 09:16:22.802] - <0><1951089590154240000> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:16:22.805] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:16:22.827] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-01 09:16:22.857] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:16:22.881] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-01 09:16:22.884] - <0><1951089591781629952> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-01 09:16:22.900] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:16:22.905] - <0><1951089591781629952> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-01 09:16:22.918] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-01 09:16:22.939] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:16:22.967] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-01 09:16:22.987] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:16:23.003] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-01 09:16:23.023] - <0><1951089590183600129> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:16:24.164] - <0><1951089596307283969> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 09:16:24.261] - <0><1951089596307283968> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-01 09:16:24.284] - <0><1951089596307283968> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-01 09:16:24.304] - <0><1951089596307283968> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-01 09:16:24.342] - <0><1951089596307283969> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 09:16:24.350] - <0><1951089596307283968> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-01 09:16:24.363] - <0><1951089596307283968> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-01 09:16:24.374] - <0><1951089596307283968> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-01 09:16:24.409] - <0><1951089596307283968> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-01 09:16:24.465] - <0><1951089596307283968> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-01 09:16:24.480] - <0><1951089596307283968> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-01 09:16:24.496] - <0><1951089596307283968> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-01 09:16:24.822] - <0><1951089596307283968> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-01 09:16:24.857] - <0><1951089596307283968> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-01 09:16:24.882] - <0><1951089596307283968> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-01 09:16:24.906] - <0><1951089596307283968> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 09:16:24.925] - <0><1951089596307283968> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-01 09:16:24.947] - <0><1951089596307283968> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-01 09:16:24.968] - <0><1951089596307283968> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-01 09:16:25.040] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.065] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.088] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.114] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.138] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.161] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.252] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.282] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.308] - <0><1951089596307283968> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:16:25.392] - <0><1951089596307283968> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-01 09:16:25.415] - <0><1951089596307283968> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:16:25.436] - <0><1951089596307283968> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:16:25.459] - <0><1951089596307283968> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:16:25.489] - <0><1951089596307283968> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:25:09.456] - <0><1951091799013138432> select '1' from dual
[INFO ] [2025-08-01 09:25:09.472] - <0><1951091799013138432> SELECT id, tenant_p_id, tenant_id, org_id, org_code, org_name, demand_no, demand_type, demand_class_type, is_need_up_file, applicant_id, applicant_code, applicant, apply_date, apply_dept_id, apply_dept_code, apply_dept_name, pur_id, pur_code, pur_name, demand_file_url, is_valid, delete_flag, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand WHERE id = 144
[INFO ] [2025-08-01 09:25:09.507] - <0><1951091798895697921> select '1' from dual
[INFO ] [2025-08-01 09:25:09.522] - <0><1951091798895697921> SELECT COUNT(*) FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2'
[INFO ] [2025-08-01 09:25:09.540] - <0><1951091799013138432> SELECT id, tenant_p_id, tenant_id, demand_id, source_item_id, goods_id, goods_code, goods_erp_code, goods_name, goods_model, vendor_id, vendor_code, vendor_name, pur_id, pur_code, pur_name, demand_date, demand_qty, sale_dept_id, sale_dept_code, sale_dept_name, model, purpose, uom_id, uom_code, uom_name, case_date, case_stat, return_cause, is_valid, delete_flag, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_item WHERE (demand_id = 144)
[INFO ] [2025-08-01 09:25:09.558] - <0><1951091798895697921> SELECT bsd.tenant_p_id, bsd.tenant_id, bsd.id, bsd.org_id, bsd.org_code, bsd.org_name, bsd.demand_no, bsd.demand_type, bsd.demand_class_type, bsd.is_need_up_file, bsd.applicant_id, bsd.applicant_code, bsd.applicant, bsd.apply_date, bsd.apply_dept_id, bsd.apply_dept_code, bsd.apply_dept_name, bsd.demand_file_url, bsd.is_valid, bsd.delete_flag, bsd.remark, bsd.create_id, bsd.creater, bsd.create_date, bsd.modifi_id, bsd.modifier, bsd.modify_date, bsdi.id AS item_id, bsdi.demand_id, bsdi.goods_id, bsdi.goods_code, bsdi.goods_erp_code, bsdi.goods_name, bsdi.goods_model, bsdi.vendor_id, bsdi.vendor_code, bsdi.vendor_name, bsdi.demand_date, bsdi.demand_qty, bsdi.sale_dept_id, bsdi.sale_dept_code, bsdi.sale_dept_name, bsdi.model, bsdi.purpose, bsdi.pur_id, bsdi.pur_code, bsdi.pur_name, bsdi.uom_id, bsdi.uom_code, bsdi.uom_name, bsdi.case_date, bsdi.source_item_id, bsdi.case_stat, bsdi.return_cause, bsdi.is_valid AS item_is_valid, bsdi.delete_flag AS item_delete_flag, bsdi.remark AS item_remark, bsdi.create_id AS item_create_id, bsdi.creater AS item_creater, bsdi.create_date AS item_create_date, bsdi.modifi_id AS item_modifi_id, bsdi.modifier AS item_modifier, bsdi.modify_date AS item_modify_date FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2' ORDER BY bsd.id DESC LIMIT 20
[INFO ] [2025-08-01 09:25:09.643] - <0><1951091799013138432> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND is_valid = 1 AND delete_flag = 0)
[INFO ] [2025-08-01 09:25:09.761] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244925)
[INFO ] [2025-08-01 09:25:09.780] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 247560)
[INFO ] [2025-08-01 09:25:09.797] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 140 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:09.821] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 137 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:09.840] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1185170)
[INFO ] [2025-08-01 09:25:09.889] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1145426)
[INFO ] [2025-08-01 09:25:09.914] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 135 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:10.028] - <0><1951091798895697920> select '1' from dual
[INFO ] [2025-08-01 09:25:10.028] - <0><1951091798899892224> select '1' from dual
[INFO ] [2025-08-01 09:25:10.028] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1211204)
[INFO ] [2025-08-01 09:25:10.040] - <0><1951091798899892224> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'base_sample' AND head_id = '144' AND line_id = '2')
[INFO ] [2025-08-01 09:25:10.041] - <0><1951091798895697920> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'base_sample' AND head_id = '144' AND line_id = '1')
[INFO ] [2025-08-01 09:25:10.047] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 113 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:10.063] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 112 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:10.089] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 111 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:10.109] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081839)
[INFO ] [2025-08-01 09:25:10.127] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1091243)
[INFO ] [2025-08-01 09:25:10.150] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081534)
[INFO ] [2025-08-01 09:25:10.169] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 72 AND goods_id = 1111750)
[INFO ] [2025-08-01 09:25:10.186] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1107349)
[INFO ] [2025-08-01 09:25:10.202] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1114845)
[INFO ] [2025-08-01 09:25:10.226] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1226897)
[INFO ] [2025-08-01 09:25:10.227] - <0><1951091803790450688> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'base_sample' AND head_id = '144' AND line_id = '2')
[INFO ] [2025-08-01 09:25:10.227] - <0><1951091803786256384> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'base_sample' AND head_id = '144' AND line_id = '1')
[INFO ] [2025-08-01 09:25:10.248] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 1117584)
[INFO ] [2025-08-01 09:25:10.268] - <0><1951091798895697921> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245853)
[INFO ] [2025-08-01 09:25:12.258] - <0><1951091812262944768> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-01 09:25:12.262] - <0><1951091812304887808> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 09:25:12.276] - <0><1951091812262944768> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-01 09:25:12.276] - <0><1951091812304887808> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 09:25:12.294] - <0><1951091812262944768> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-01 09:25:12.312] - <0><1951091812262944768> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-01 09:25:12.329] - <0><1951091812262944768> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-01 09:25:12.342] - <0><1951091812262944768> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-01 09:25:12.358] - <0><1951091812262944768> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-01 09:25:12.369] - <0><1951091812262944768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-01 09:25:12.381] - <0><1951091812262944768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-01 09:25:12.396] - <0><1951091812262944768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-01 09:25:12.496] - <0><1951091812262944768> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-01 09:25:12.535] - <0><1951091812262944768> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-01 09:25:12.557] - <0><1951091812262944768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-01 09:25:12.579] - <0><1951091812262944768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 09:25:12.597] - <0><1951091812262944768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-01 09:25:12.616] - <0><1951091812262944768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-01 09:25:12.633] - <0><1951091812262944768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-01 09:25:12.676] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.698] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.719] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.741] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.761] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.781] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.802] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.824] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.844] - <0><1951091812262944768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:25:12.870] - <0><1951091812262944768> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-01 09:25:12.884] - <0><1951091812262944768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:25:12.909] - <0><1951091812262944768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:25:12.931] - <0><1951091812262944768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:25:12.951] - <0><1951091812262944768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:25:45.051] - <0><1951091949706092544> SELECT COUNT(*) FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2'
[INFO ] [2025-08-01 09:25:45.089] - <0><1951091949706092544> SELECT bsd.tenant_p_id, bsd.tenant_id, bsd.id, bsd.org_id, bsd.org_code, bsd.org_name, bsd.demand_no, bsd.demand_type, bsd.demand_class_type, bsd.is_need_up_file, bsd.applicant_id, bsd.applicant_code, bsd.applicant, bsd.apply_date, bsd.apply_dept_id, bsd.apply_dept_code, bsd.apply_dept_name, bsd.demand_file_url, bsd.is_valid, bsd.delete_flag, bsd.remark, bsd.create_id, bsd.creater, bsd.create_date, bsd.modifi_id, bsd.modifier, bsd.modify_date, bsdi.id AS item_id, bsdi.demand_id, bsdi.goods_id, bsdi.goods_code, bsdi.goods_erp_code, bsdi.goods_name, bsdi.goods_model, bsdi.vendor_id, bsdi.vendor_code, bsdi.vendor_name, bsdi.demand_date, bsdi.demand_qty, bsdi.sale_dept_id, bsdi.sale_dept_code, bsdi.sale_dept_name, bsdi.model, bsdi.purpose, bsdi.pur_id, bsdi.pur_code, bsdi.pur_name, bsdi.uom_id, bsdi.uom_code, bsdi.uom_name, bsdi.case_date, bsdi.source_item_id, bsdi.case_stat, bsdi.return_cause, bsdi.is_valid AS item_is_valid, bsdi.delete_flag AS item_delete_flag, bsdi.remark AS item_remark, bsdi.create_id AS item_create_id, bsdi.creater AS item_creater, bsdi.create_date AS item_create_date, bsdi.modifi_id AS item_modifi_id, bsdi.modifier AS item_modifier, bsdi.modify_date AS item_modify_date FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2' ORDER BY bsd.id DESC LIMIT 20
[INFO ] [2025-08-01 09:25:45.116] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244925)
[INFO ] [2025-08-01 09:25:45.134] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 247560)
[INFO ] [2025-08-01 09:25:45.153] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 140 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:45.170] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 137 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:45.190] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1185170)
[INFO ] [2025-08-01 09:25:45.204] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1145426)
[INFO ] [2025-08-01 09:25:45.234] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 135 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:45.250] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1211204)
[INFO ] [2025-08-01 09:25:45.271] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 113 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:45.293] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 112 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:45.315] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 111 AND goods_id = 245234)
[INFO ] [2025-08-01 09:25:45.335] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1091243)
[INFO ] [2025-08-01 09:25:45.353] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081534)
[INFO ] [2025-08-01 09:25:45.370] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081839)
[INFO ] [2025-08-01 09:25:45.388] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 72 AND goods_id = 1111750)
[INFO ] [2025-08-01 09:25:45.404] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1107349)
[INFO ] [2025-08-01 09:25:45.420] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1226897)
[INFO ] [2025-08-01 09:25:45.437] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1114845)
[INFO ] [2025-08-01 09:25:45.455] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 1117584)
[INFO ] [2025-08-01 09:25:45.470] - <0><1951091949706092544> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245853)
[INFO ] [2025-08-01 09:26:50.053] - <0><1951092222340046848> select '1' from dual
[INFO ] [2025-08-01 09:26:50.071] - <0><1951092222340046848> SELECT COUNT(*) FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2'
[INFO ] [2025-08-01 09:26:50.173] - <0><1951092222340046848> SELECT bsd.tenant_p_id, bsd.tenant_id, bsd.id, bsd.org_id, bsd.org_code, bsd.org_name, bsd.demand_no, bsd.demand_type, bsd.demand_class_type, bsd.is_need_up_file, bsd.applicant_id, bsd.applicant_code, bsd.applicant, bsd.apply_date, bsd.apply_dept_id, bsd.apply_dept_code, bsd.apply_dept_name, bsd.demand_file_url, bsd.is_valid, bsd.delete_flag, bsd.remark, bsd.create_id, bsd.creater, bsd.create_date, bsd.modifi_id, bsd.modifier, bsd.modify_date, bsdi.id AS item_id, bsdi.demand_id, bsdi.goods_id, bsdi.goods_code, bsdi.goods_erp_code, bsdi.goods_name, bsdi.goods_model, bsdi.vendor_id, bsdi.vendor_code, bsdi.vendor_name, bsdi.demand_date, bsdi.demand_qty, bsdi.sale_dept_id, bsdi.sale_dept_code, bsdi.sale_dept_name, bsdi.model, bsdi.purpose, bsdi.pur_id, bsdi.pur_code, bsdi.pur_name, bsdi.uom_id, bsdi.uom_code, bsdi.uom_name, bsdi.case_date, bsdi.source_item_id, bsdi.case_stat, bsdi.return_cause, bsdi.is_valid AS item_is_valid, bsdi.delete_flag AS item_delete_flag, bsdi.remark AS item_remark, bsdi.create_id AS item_create_id, bsdi.creater AS item_creater, bsdi.create_date AS item_create_date, bsdi.modifi_id AS item_modifi_id, bsdi.modifier AS item_modifier, bsdi.modify_date AS item_modify_date FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2' ORDER BY bsd.id DESC LIMIT 20
[INFO ] [2025-08-01 09:26:50.197] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244925)
[INFO ] [2025-08-01 09:26:50.219] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 247560)
[INFO ] [2025-08-01 09:26:50.243] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 140 AND goods_id = 245234)
[INFO ] [2025-08-01 09:26:50.264] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 137 AND goods_id = 245234)
[INFO ] [2025-08-01 09:26:50.281] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1185170)
[INFO ] [2025-08-01 09:26:50.301] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1145426)
[INFO ] [2025-08-01 09:26:50.318] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 135 AND goods_id = 245234)
[INFO ] [2025-08-01 09:26:50.338] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1211204)
[INFO ] [2025-08-01 09:26:50.357] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 113 AND goods_id = 245234)
[INFO ] [2025-08-01 09:26:50.376] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 112 AND goods_id = 245234)
[INFO ] [2025-08-01 09:26:50.393] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 111 AND goods_id = 245234)
[INFO ] [2025-08-01 09:26:50.412] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1091243)
[INFO ] [2025-08-01 09:26:50.429] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081534)
[INFO ] [2025-08-01 09:26:50.446] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081839)
[INFO ] [2025-08-01 09:26:50.463] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 72 AND goods_id = 1111750)
[INFO ] [2025-08-01 09:26:50.483] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1107349)
[INFO ] [2025-08-01 09:26:50.498] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1226897)
[INFO ] [2025-08-01 09:26:50.520] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1114845)
[INFO ] [2025-08-01 09:26:50.532] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 1117584)
[INFO ] [2025-08-01 09:26:50.556] - <0><1951092222340046848> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245853)
[INFO ] [2025-08-01 09:27:01.000] - <0><1951092268187983872> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:27:01.059] - <0><1951092268187983872> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:27:01.812] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:27:01.815] - <0><1951092271228854273> select '1' from dual
[INFO ] [2025-08-01 09:27:01.828] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-01 09:27:01.828] - <0><1951092271228854273> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:27:01.846] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:27:01.862] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-01 09:27:01.888] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:27:01.912] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-01 09:27:01.932] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:27:01.980] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-01 09:27:02.029] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:27:02.044] - <0><1951092272763969536> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-01 09:27:02.059] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-01 09:27:02.060] - <0><1951092272763969536> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-01 09:27:02.080] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:27:02.094] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-01 09:27:02.110] - <0><1951092271224659968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:27:03.661] - <0><1951092279386775552> SELECT COUNT(*) FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2'
[INFO ] [2025-08-01 09:27:03.780] - <0><1951092279386775552> SELECT bsd.tenant_p_id, bsd.tenant_id, bsd.id, bsd.org_id, bsd.org_code, bsd.org_name, bsd.demand_no, bsd.demand_type, bsd.demand_class_type, bsd.is_need_up_file, bsd.applicant_id, bsd.applicant_code, bsd.applicant, bsd.apply_date, bsd.apply_dept_id, bsd.apply_dept_code, bsd.apply_dept_name, bsd.demand_file_url, bsd.is_valid, bsd.delete_flag, bsd.remark, bsd.create_id, bsd.creater, bsd.create_date, bsd.modifi_id, bsd.modifier, bsd.modify_date, bsdi.id AS item_id, bsdi.demand_id, bsdi.goods_id, bsdi.goods_code, bsdi.goods_erp_code, bsdi.goods_name, bsdi.goods_model, bsdi.vendor_id, bsdi.vendor_code, bsdi.vendor_name, bsdi.demand_date, bsdi.demand_qty, bsdi.sale_dept_id, bsdi.sale_dept_code, bsdi.sale_dept_name, bsdi.model, bsdi.purpose, bsdi.pur_id, bsdi.pur_code, bsdi.pur_name, bsdi.uom_id, bsdi.uom_code, bsdi.uom_name, bsdi.case_date, bsdi.source_item_id, bsdi.case_stat, bsdi.return_cause, bsdi.is_valid AS item_is_valid, bsdi.delete_flag AS item_delete_flag, bsdi.remark AS item_remark, bsdi.create_id AS item_create_id, bsdi.creater AS item_creater, bsdi.create_date AS item_create_date, bsdi.modifi_id AS item_modifi_id, bsdi.modifier AS item_modifier, bsdi.modify_date AS item_modify_date FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2' ORDER BY bsd.id DESC LIMIT 20
[INFO ] [2025-08-01 09:27:03.808] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:03.827] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 247560)
[INFO ] [2025-08-01 09:27:03.847] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 140 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:03.865] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 137 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:03.882] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1185170)
[INFO ] [2025-08-01 09:27:03.897] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1145426)
[INFO ] [2025-08-01 09:27:03.930] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 135 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:03.950] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1211204)
[INFO ] [2025-08-01 09:27:03.971] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 113 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:03.991] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 112 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:04.008] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 111 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:04.026] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1091243)
[INFO ] [2025-08-01 09:27:04.041] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081534)
[INFO ] [2025-08-01 09:27:04.057] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081839)
[INFO ] [2025-08-01 09:27:04.073] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 72 AND goods_id = 1111750)
[INFO ] [2025-08-01 09:27:04.090] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1107349)
[INFO ] [2025-08-01 09:27:04.110] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1226897)
[INFO ] [2025-08-01 09:27:04.129] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1114845)
[INFO ] [2025-08-01 09:27:04.147] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 1117584)
[INFO ] [2025-08-01 09:27:04.169] - <0><1951092279386775552> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245853)
[INFO ] [2025-08-01 09:27:23.689] - <0><1951092363444822016> SELECT COUNT(*) FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2'
[INFO ] [2025-08-01 09:27:23.728] - <0><1951092363444822016> SELECT bsd.tenant_p_id, bsd.tenant_id, bsd.id, bsd.org_id, bsd.org_code, bsd.org_name, bsd.demand_no, bsd.demand_type, bsd.demand_class_type, bsd.is_need_up_file, bsd.applicant_id, bsd.applicant_code, bsd.applicant, bsd.apply_date, bsd.apply_dept_id, bsd.apply_dept_code, bsd.apply_dept_name, bsd.demand_file_url, bsd.is_valid, bsd.delete_flag, bsd.remark, bsd.create_id, bsd.creater, bsd.create_date, bsd.modifi_id, bsd.modifier, bsd.modify_date, bsdi.id AS item_id, bsdi.demand_id, bsdi.goods_id, bsdi.goods_code, bsdi.goods_erp_code, bsdi.goods_name, bsdi.goods_model, bsdi.vendor_id, bsdi.vendor_code, bsdi.vendor_name, bsdi.demand_date, bsdi.demand_qty, bsdi.sale_dept_id, bsdi.sale_dept_code, bsdi.sale_dept_name, bsdi.model, bsdi.purpose, bsdi.pur_id, bsdi.pur_code, bsdi.pur_name, bsdi.uom_id, bsdi.uom_code, bsdi.uom_name, bsdi.case_date, bsdi.source_item_id, bsdi.case_stat, bsdi.return_cause, bsdi.is_valid AS item_is_valid, bsdi.delete_flag AS item_delete_flag, bsdi.remark AS item_remark, bsdi.create_id AS item_create_id, bsdi.creater AS item_creater, bsdi.create_date AS item_create_date, bsdi.modifi_id AS item_modifi_id, bsdi.modifier AS item_modifier, bsdi.modify_date AS item_modify_date FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsdi.case_stat = '1' AND bsd.demand_class_type = '2' ORDER BY bsd.id DESC LIMIT 100
[INFO ] [2025-08-01 09:27:23.759] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:23.779] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 247560)
[INFO ] [2025-08-01 09:27:23.797] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 140 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:23.814] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 137 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:23.832] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1185170)
[INFO ] [2025-08-01 09:27:23.853] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1145426)
[INFO ] [2025-08-01 09:27:23.887] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 135 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:23.915] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1211204)
[INFO ] [2025-08-01 09:27:23.933] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 113 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:23.953] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 112 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:23.979] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 111 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:23.999] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1091243)
[INFO ] [2025-08-01 09:27:24.019] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081534)
[INFO ] [2025-08-01 09:27:24.047] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081839)
[INFO ] [2025-08-01 09:27:24.068] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 72 AND goods_id = 1111750)
[INFO ] [2025-08-01 09:27:24.087] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1107349)
[INFO ] [2025-08-01 09:27:24.105] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1226897)
[INFO ] [2025-08-01 09:27:24.120] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1114845)
[INFO ] [2025-08-01 09:27:24.147] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 1117584)
[INFO ] [2025-08-01 09:27:24.173] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245853)
[INFO ] [2025-08-01 09:27:24.515] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245744)
[INFO ] [2025-08-01 09:27:24.534] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 55 AND goods_id = 1133734)
[INFO ] [2025-08-01 09:27:24.550] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 55 AND goods_id = 245477)
[INFO ] [2025-08-01 09:27:24.572] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 55 AND goods_id = 245430)
[INFO ] [2025-08-01 09:27:24.587] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 54 AND goods_id = 1088400)
[INFO ] [2025-08-01 09:27:24.613] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 54 AND goods_id = 1127546)
[INFO ] [2025-08-01 09:27:24.631] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 45 AND goods_id = 1087867)
[INFO ] [2025-08-01 09:27:24.655] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 45 AND goods_id = 1103087)
[INFO ] [2025-08-01 09:27:24.684] - <0><1951092363444822016> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 45 AND goods_id = 1133773)
[INFO ] [2025-08-01 09:27:30.245] - <0><1951092390963650560> SELECT COUNT(*) FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsd.demand_class_type = '2'
[INFO ] [2025-08-01 09:27:30.304] - <0><1951092390963650560> SELECT bsd.tenant_p_id, bsd.tenant_id, bsd.id, bsd.org_id, bsd.org_code, bsd.org_name, bsd.demand_no, bsd.demand_type, bsd.demand_class_type, bsd.is_need_up_file, bsd.applicant_id, bsd.applicant_code, bsd.applicant, bsd.apply_date, bsd.apply_dept_id, bsd.apply_dept_code, bsd.apply_dept_name, bsd.demand_file_url, bsd.is_valid, bsd.delete_flag, bsd.remark, bsd.create_id, bsd.creater, bsd.create_date, bsd.modifi_id, bsd.modifier, bsd.modify_date, bsdi.id AS item_id, bsdi.demand_id, bsdi.goods_id, bsdi.goods_code, bsdi.goods_erp_code, bsdi.goods_name, bsdi.goods_model, bsdi.vendor_id, bsdi.vendor_code, bsdi.vendor_name, bsdi.demand_date, bsdi.demand_qty, bsdi.sale_dept_id, bsdi.sale_dept_code, bsdi.sale_dept_name, bsdi.model, bsdi.purpose, bsdi.pur_id, bsdi.pur_code, bsdi.pur_name, bsdi.uom_id, bsdi.uom_code, bsdi.uom_name, bsdi.case_date, bsdi.source_item_id, bsdi.case_stat, bsdi.return_cause, bsdi.is_valid AS item_is_valid, bsdi.delete_flag AS item_delete_flag, bsdi.remark AS item_remark, bsdi.create_id AS item_create_id, bsdi.creater AS item_creater, bsdi.create_date AS item_create_date, bsdi.modifi_id AS item_modifi_id, bsdi.modifier AS item_modifier, bsdi.modify_date AS item_modify_date FROM base_sample_demand bsd JOIN base_sample_demand_item bsdi ON bsd.id = bsdi.demand_id WHERE bsd.delete_flag = 0 AND bsdi.delete_flag = 0 AND bsd.demand_class_type = '2' ORDER BY bsd.id DESC LIMIT 100
[INFO ] [2025-08-01 09:27:30.337] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 149 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:30.358] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 149 AND goods_id = 244922)
[INFO ] [2025-08-01 09:27:30.374] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 148 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:30.394] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 148 AND goods_id = 244922)
[INFO ] [2025-08-01 09:27:30.413] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244927)
[INFO ] [2025-08-01 09:27:30.427] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 145 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:30.452] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 247560)
[INFO ] [2025-08-01 09:27:30.461] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 144 AND goods_id = 1157940)
[INFO ] [2025-08-01 09:27:30.486] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 140 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:30.502] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 139 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:30.522] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 137 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:30.538] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1185170)
[INFO ] [2025-08-01 09:27:30.556] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 136 AND goods_id = 1145426)
[INFO ] [2025-08-01 09:27:30.574] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 135 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:30.592] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 134 AND goods_id = 244926)
[INFO ] [2025-08-01 09:27:30.611] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 132 AND goods_id = 244926)
[INFO ] [2025-08-01 09:27:30.627] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 132 AND goods_id = 244927)
[INFO ] [2025-08-01 09:27:30.644] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 125 AND goods_id = 244926)
[INFO ] [2025-08-01 09:27:30.663] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 125 AND goods_id = 244925)
[INFO ] [2025-08-01 09:27:30.678] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 122 AND goods_id = 1226924)
[INFO ] [2025-08-01 09:27:30.705] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 116 AND goods_id = 1086187)
[INFO ] [2025-08-01 09:27:30.721] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 116 AND goods_id = 1083328)
[INFO ] [2025-08-01 09:27:30.739] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 115 AND goods_id = 1152349)
[INFO ] [2025-08-01 09:27:30.755] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 115 AND goods_id = 1147360)
[INFO ] [2025-08-01 09:27:30.772] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1105509)
[INFO ] [2025-08-01 09:27:30.790] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 114 AND goods_id = 1211204)
[INFO ] [2025-08-01 09:27:30.810] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 113 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:30.828] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 112 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:30.843] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 111 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:30.863] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 109 AND goods_id = 1159448)
[INFO ] [2025-08-01 09:27:30.878] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 108 AND goods_id = 1210563)
[INFO ] [2025-08-01 09:27:30.899] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 107 AND goods_id = 1150804)
[INFO ] [2025-08-01 09:27:30.920] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 107 AND goods_id = 1151046)
[INFO ] [2025-08-01 09:27:30.941] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 104 AND goods_id = 244922)
[INFO ] [2025-08-01 09:27:30.964] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 85 AND goods_id = 1102295)
[INFO ] [2025-08-01 09:27:30.978] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 85 AND goods_id = 1067441)
[INFO ] [2025-08-01 09:27:31.000] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 85 AND goods_id = 1067271)
[INFO ] [2025-08-01 09:27:31.022] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081839)
[INFO ] [2025-08-01 09:27:31.039] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1091243)
[INFO ] [2025-08-01 09:27:31.055] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081534)
[INFO ] [2025-08-01 09:27:31.072] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 84 AND goods_id = 1081131)
[INFO ] [2025-08-01 09:27:31.091] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 82 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:31.110] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 80 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:31.127] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 79 AND goods_id = 245235)
[INFO ] [2025-08-01 09:27:31.149] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 78 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:31.170] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 74 AND goods_id = 1226885)
[INFO ] [2025-08-01 09:27:31.189] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 74 AND goods_id = 245234)
[INFO ] [2025-08-01 09:27:31.205] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 74 AND goods_id = 1106772)
[INFO ] [2025-08-01 09:27:31.232] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 72 AND goods_id = 1111750)
[INFO ] [2025-08-01 09:27:31.245] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 70 AND goods_id = 1096780)
[INFO ] [2025-08-01 09:27:31.267] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 70 AND goods_id = 1111552)
[INFO ] [2025-08-01 09:27:31.288] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 70 AND goods_id = 1099103)
[INFO ] [2025-08-01 09:27:31.304] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1107349)
[INFO ] [2025-08-01 09:27:31.321] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 68 AND goods_id = 1105869)
[INFO ] [2025-08-01 09:27:31.344] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1226897)
[INFO ] [2025-08-01 09:27:31.362] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 65 AND goods_id = 1114845)
[INFO ] [2025-08-01 09:27:31.379] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 64 AND goods_id = 1226896)
[INFO ] [2025-08-01 09:27:31.396] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 64 AND goods_id = 1226895)
[INFO ] [2025-08-01 09:27:31.416] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 64 AND goods_id = 1226894)
[INFO ] [2025-08-01 09:27:31.434] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245853)
[INFO ] [2025-08-01 09:27:31.454] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 245744)
[INFO ] [2025-08-01 09:27:31.474] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 62 AND goods_id = 1117584)
[INFO ] [2025-08-01 09:27:31.491] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 57 AND goods_id = 1226849)
[INFO ] [2025-08-01 09:27:31.511] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 55 AND goods_id = 245430)
[INFO ] [2025-08-01 09:27:31.528] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 55 AND goods_id = 1133734)
[INFO ] [2025-08-01 09:27:31.546] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 55 AND goods_id = 245477)
[INFO ] [2025-08-01 09:27:31.569] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 54 AND goods_id = 1088400)
[INFO ] [2025-08-01 09:27:31.587] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 54 AND goods_id = 1127546)
[INFO ] [2025-08-01 09:27:31.604] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 45 AND goods_id = 1133773)
[INFO ] [2025-08-01 09:27:31.623] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 45 AND goods_id = 1087867)
[INFO ] [2025-08-01 09:27:31.640] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 45 AND goods_id = 1103087)
[INFO ] [2025-08-01 09:27:31.659] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 33 AND goods_id = 1226891)
[INFO ] [2025-08-01 09:27:31.676] - <0><1951092390963650560> SELECT id, tenant_p_id, tenant_id, demand_id, demand_item_id, goods_id, goods_erp_code, goods_name, vendor_id, vendor_code, vendor_name, is_assigned, is_return, return_remark, case_date, case_stat, is_valid, delete_flag, remark, assigner, assign_date, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_sample_demand_vendor WHERE (demand_id = 33 AND goods_id = 1226888)
[INFO ] [2025-08-01 09:27:47.398] - <0><1951092462912741376> SELECT COUNT(*) FROM base_sample bs WHERE bs.tenant_id = 24739
[INFO ] [2025-08-01 09:27:47.436] - <0><1951092462912741376> SELECT bs.id, bs.tenant_id, bs.vendor_id, bs.vendor_code, bs.vendor_erp_code, bs.vendor_name, bs.dept_id, bs.dept_code, bs.dept_name, bs.sample_no, bs.sample_date, bs.sample_stat, bs.source_id, bs.source_no, bs.source_type, bs.demand_class_type, bs.is_need_up_file, bs.is_valid, bs.delete_flag, bs.return_remark, bs.is_return, bs.return_date, bs.remark, bs.create_id, bs.creater, bs.create_date, bs.modifi_id, bs.modifier, bs.modify_date, bs.tenant_name, bsi.id item_id, bsi.goods_id, bsi.goods_code, bsi.goods_erp_code, bsi.goods_name, bsi.goods_model, bsi.demand_date, bsi.demand_qty, bsi.reply_quantity, bsi.reply_delivery_date, bsi.reply_state, bsi.pur_name, bsi.goods_num, bsi.item_stat, bsi.case_stat, bsi.case_date, bsi.vendor_remark, bsi.remark item_remark, (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = bs.id) if_inspection FROM base_sample bs LEFT JOIN base_sample_item bsi ON bs.id = bsi.sample_id WHERE bs.tenant_id = 24739 ORDER BY bs.id DESC LIMIT 20
[INFO ] [2025-08-01 09:27:59.946] - <0><1951092513936449536> SELECT COUNT(*) FROM base_vendor_exit bve
[INFO ] [2025-08-01 09:28:00.005] - <0><1951092513936449536> SELECT bve.id, bve.vendor_exit_no AS vendorExitNo, bve.wf_status AS wfStatus, bv.vendor_code AS vendorCode, bv.vendor_erp_code AS vendorErpCode, bv.vendor_name AS vendorName, bv.vendor_full_name AS vendorFullName, bv.vendor_full_address AS vendorFullAddress, bv.bus_number AS busNumber, bv.vendor_email AS vendorEmail, bv.vendor_bank_number AS vendorBankNumber, bv.vendor_account_name AS vendorAccountName, bve.exit_date AS quitDate, bve.modifier AS modifier, bve.modify_date AS modifyDate FROM base_vendor_exit bve LEFT JOIN base_vendor bv ON bve.vendor_id = bv.soure_id ORDER BY bve.id DESC LIMIT 9223372036854775807
[INFO ] [2025-08-01 09:28:09.783] - <0><1951092556642852864> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:28:09.866] - <0><1951092556642852864> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 20
[INFO ] [2025-08-01 09:28:11.708] - <0><1951092564947574784> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:28:11.726] - <0><1951092564947574784> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 20
[INFO ] [2025-08-01 09:28:15.252] - <0><1951092579598278656> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
[INFO ] [2025-08-01 09:28:15.287] - <0><1951092579598278656> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-01 09:28:18.194] - <0><1951092591778537472> SELECT COUNT(*) FROM dm_master sm, dm_master_item smi WHERE sm.id = smi.master_id AND sm.delete_flag = 0 AND smi.delete_flag = 0 AND sm.tenant_id = 24739 AND sm.dept_id = '28905'
[INFO ] [2025-08-01 09:28:18.219] - <0><1951092591778537472> SELECT sm.id, sm.tenant_id tenantId, sm.master_type masterType, sm.vendor_id vendorId, sm.vendor_code vendorCode, sm.vendor_name vendorName, smi.de_id deliveryId, smi.de_item_id deliveryItemId, smi.de_seq, smi.de_no, smi.sale_id, smi.sale_seq, smi.sale_no, sm.master_no masterNo, smi.seq masterSeq, smi.goods_id goodsId, smi.goods_code goodsCode, smi.goods_erp_code goodsErpCode, smi.goods_name goodsName, smi.goods_model goodsModel, smi.master_num masterNum, smi.drawing_no drawingNo, sm.create_date createDate, smi.statements_no statementsNo, sm.tenant_name tenantName, sm.post_date postDate, smi.master_Date masterDate, smi.source_erp_no, smi.sap_doc_row_no, IFNULL(smi.sap_reference_no, ''), IFNULL(smi.sap_reference_row_no, ''), sm.master_stat, sm.dept_id, sm.dept_code, sm.dept_name, (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = sm.id) if_inspection, smi.uom_id, smi.uom_code, smi.uom_name, smi.uom_num, smi.aux_uom_id, smi.aux_uom_code, smi.aux_uom_name, smi.aux_uom_num, sm.reserved06, (SELECT opi.return_mark FROM order_pur_item opi WHERE opi.id = smi.sale_item_id) return_mark, (SELECT opi.free_mark FROM order_pur_item opi WHERE opi.id = smi.sale_item_id) free_mark FROM dm_master sm, dm_master_item smi WHERE sm.id = smi.master_id AND sm.delete_flag = 0 AND smi.delete_flag = 0 AND sm.tenant_id = 24739 AND sm.dept_id = '28905' ORDER BY sm.id DESC LIMIT 20
[INFO ] [2025-08-01 09:28:18.263] - <0><1951092591778537472> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (id IN (5807, 5808, 5796, 5797))
[INFO ] [2025-08-01 09:28:20.039] - <0><1951092599693189120> SELECT COUNT(*) FROM dm_inventory WHERE (tenant_id = 24739 AND delete_flag = 0)
[INFO ] [2025-08-01 09:28:20.052] - <0><1951092599693189120> SELECT id, tenant_p_id, tenant_id, org_id, org_code, org_name, vendor_id, vendor_code, vendor_name, goods_id, goods_erp_code, goods_name, goods_model, inv_qty, ori_inv_qty, is_valid, delete_flag, uom_name, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_inventory WHERE (tenant_id = 24739 AND delete_flag = 0) ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 09:28:20.079] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 24899 AND opi.goods_id = 244922
[INFO ] [2025-08-01 09:28:20.114] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 24899 AND opi.goods_id = 244922
[INFO ] [2025-08-01 09:28:20.135] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25254 AND opi.goods_id = 244925
[INFO ] [2025-08-01 09:28:20.157] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25254 AND opi.goods_id = 244925
[INFO ] [2025-08-01 09:28:20.181] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 24964 AND opi.goods_id = 244922
[INFO ] [2025-08-01 09:28:20.203] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 24964 AND opi.goods_id = 244922
[INFO ] [2025-08-01 09:28:20.226] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25411 AND opi.goods_id = 244922
[INFO ] [2025-08-01 09:28:20.249] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25411 AND opi.goods_id = 244922
[INFO ] [2025-08-01 09:28:20.268] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25409 AND opi.goods_id = 244925
[INFO ] [2025-08-01 09:28:20.286] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25409 AND opi.goods_id = 244925
[INFO ] [2025-08-01 09:28:20.316] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25414 AND opi.goods_id = 305
[INFO ] [2025-08-01 09:28:20.334] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 25414 AND opi.goods_id = 305
[INFO ] [2025-08-01 09:28:20.352] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 304
[INFO ] [2025-08-01 09:28:20.378] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 304
[INFO ] [2025-08-01 09:28:20.395] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 303
[INFO ] [2025-08-01 09:28:20.417] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 303
[INFO ] [2025-08-01 09:28:20.435] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 302
[INFO ] [2025-08-01 09:28:20.453] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 302
[INFO ] [2025-08-01 09:28:20.581] - <0><1951092599693189120> SELECT SUM(opi.order_num) AS order_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 301
[INFO ] [2025-08-01 09:28:20.603] - <0><1951092599693189120> SELECT (SUM(opi.order_num) - SUM(opi.erp_master_num)) AS order_undelivered_qua FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.tenant_id = 24739 AND op.vendor_id = 101 AND opi.goods_id = 301
[INFO ] [2025-08-01 09:28:21.501] - <0><1951092605770735616> SELECT COUNT(*) FROM dm_inspection_sheet WHERE tenant_id = 24739
[INFO ] [2025-08-01 09:31:05.960] - <0><1951093295805046784> select '1' from dual
[INFO ] [2025-08-01 09:31:05.988] - <0><1951093295805046784> SELECT COUNT(*) FROM base_sample bs WHERE bs.tenant_id = 24739
[INFO ] [2025-08-01 09:31:06.055] - <0><1951093295805046784> SELECT bs.id, bs.tenant_id, bs.vendor_id, bs.vendor_code, bs.vendor_erp_code, bs.vendor_name, bs.dept_id, bs.dept_code, bs.dept_name, bs.sample_no, bs.sample_date, bs.sample_stat, bs.source_id, bs.source_no, bs.source_type, bs.demand_class_type, bs.is_need_up_file, bs.is_valid, bs.delete_flag, bs.return_remark, bs.is_return, bs.return_date, bs.remark, bs.create_id, bs.creater, bs.create_date, bs.modifi_id, bs.modifier, bs.modify_date, bs.tenant_name, bsi.id item_id, bsi.goods_id, bsi.goods_code, bsi.goods_erp_code, bsi.goods_name, bsi.goods_model, bsi.demand_date, bsi.demand_qty, bsi.reply_quantity, bsi.reply_delivery_date, bsi.reply_state, bsi.pur_name, bsi.goods_num, bsi.item_stat, bsi.case_stat, bsi.case_date, bsi.vendor_remark, bsi.remark item_remark, (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = bs.id) if_inspection FROM base_sample bs LEFT JOIN base_sample_item bsi ON bs.id = bsi.sample_id WHERE bs.tenant_id = 24739 ORDER BY bs.id DESC LIMIT 20
[INFO ] [2025-08-01 09:32:11.868] - <0><1951093572222263296> select '1' from dual
[INFO ] [2025-08-01 09:32:11.884] - <0><1951093572222263296> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:32:11.904] - <0><1951093572222263296> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 20
[INFO ] [2025-08-01 09:33:54.384] - <0><1951094002272641024> select '1' from dual
[INFO ] [2025-08-01 09:33:54.392] - <0><1951094002276835328> select '1' from dual
[INFO ] [2025-08-01 09:33:54.404] - <0><1951094002276835328> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-01 09:33:54.406] - <0><1951094002272641024> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 09:33:54.419] - <0><1951094002276835328> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-01 09:33:54.421] - <0><1951094002272641024> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 09:33:54.448] - <0><1951094002276835328> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-01 09:33:54.462] - <0><1951094002276835328> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-01 09:33:54.476] - <0><1951094002276835328> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-01 09:33:54.488] - <0><1951094002276835328> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-01 09:33:54.499] - <0><1951094002276835328> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-01 09:33:54.513] - <0><1951094002276835328> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-01 09:33:54.525] - <0><1951094002276835328> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-01 09:33:54.538] - <0><1951094002276835328> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-01 09:33:54.611] - <0><1951094002276835328> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-01 09:33:54.646] - <0><1951094002276835328> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-01 09:33:54.667] - <0><1951094002276835328> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-01 09:33:54.775] - <0><1951094002276835328> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 09:33:54.808] - <0><1951094002276835328> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-01 09:33:54.828] - <0><1951094002276835328> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-01 09:33:54.845] - <0><1951094002276835328> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-01 09:33:54.893] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:54.915] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:54.937] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:54.975] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:54.993] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:55.012] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:55.040] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:55.060] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:55.081] - <0><1951094002276835328> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:33:55.102] - <0><1951094002276835328> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-01 09:33:55.126] - <0><1951094002276835328> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:33:55.145] - <0><1951094002276835328> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:33:55.164] - <0><1951094002276835328> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:33:55.185] - <0><1951094002276835328> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:37:40.987] - <0><1951094952714510336> select '1' from dual
[INFO ] [2025-08-01 09:37:43.311] - <0><1951094962382381056> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-01 09:37:43.325] - <0><1951094962382381056> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:37:43.343] - <0><1951094962382381056> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:37:44.134] - <0><1951094965863653376> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:37:44.172] - <0><1951094965863653376> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:37:44.650] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:37:44.656] - <0><1951094967981776898> select '1' from dual
[INFO ] [2025-08-01 09:37:44.663] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-01 09:37:44.672] - <0><1951094967981776898> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:37:44.681] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:37:44.695] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-01 09:37:44.924] - <0><1951094969168764928> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-01 09:37:44.931] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:37:44.940] - <0><1951094969168764928> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-01 09:37:44.945] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-01 09:37:44.965] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:37:44.983] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-01 09:37:44.998] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:37:45.020] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-01 09:37:45.031] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:37:45.051] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-01 09:37:45.065] - <0><1951094967985971200> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:37:45.699] - <0><1951094972461293569> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-01 09:37:45.707] - <0><1951094972461293568> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 09:37:45.722] - <0><1951094972461293569> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-01 09:37:45.722] - <0><1951094972461293568> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 09:37:45.741] - <0><1951094972461293569> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-01 09:37:45.753] - <0><1951094972461293569> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-01 09:37:45.767] - <0><1951094972461293569> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-01 09:37:45.778] - <0><1951094972461293569> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-01 09:37:45.790] - <0><1951094972461293569> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-01 09:37:45.800] - <0><1951094972461293569> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-01 09:37:45.817] - <0><1951094972461293569> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-01 09:37:45.832] - <0><1951094972461293569> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-01 09:37:45.975] - <0><1951094972461293569> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-01 09:37:46.000] - <0><1951094972461293569> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-01 09:37:46.021] - <0><1951094972461293569> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-01 09:37:46.045] - <0><1951094972461293569> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 09:37:46.068] - <0><1951094972461293569> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-01 09:37:46.087] - <0><1951094972461293569> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-01 09:37:46.103] - <0><1951094972461293569> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-01 09:37:46.144] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.162] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.182] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.199] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.222] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.242] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.261] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.281] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.301] - <0><1951094972461293569> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:37:46.321] - <0><1951094972461293569> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-01 09:37:46.339] - <0><1951094972461293569> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:37:46.357] - <0><1951094972461293569> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:37:46.377] - <0><1951094972461293569> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:37:46.396] - <0><1951094972461293569> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:38:26.171] - <0><1951095141693071360> SELECT COUNT(*) FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND op.delete_flag = 0 AND opi.delete_flag = 0 AND op.tenant_id = 24739 AND op.dept_id = '28905'
[INFO ] [2025-08-01 09:38:26.355] - <0><1951095141693071360> SELECT op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name, op.dept_code, op.dept_name, op.order_date, op.order_flag, op.sync_date, op.publish_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.delete_flag, op.create_id, op.creater, op.create_date, op.modifi_id, op.modifier, op.modify_date, op.dep_code, op.tenant_name, op.is_print, op.print_count, op.source_no, op.order_remark, op.order_confirm_synchronize, op.confirm_date, op.order_reply_date, op.ex_rate, op.bukrs, op.currency_code, op.bsart, op.currency_name, op.total_amount, op.pur_id, op.pur_name, op.pay_text, op.source_id, op.pay_name, op.erp_approve_date, opi.pur_id, opi.seq, opi.id AS lineId, opi.goods_id, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model, opi.uom_id, opi.uom_code, opi.uom_name, opi.rate_id, opi.rate_name, opi.rate_val, opi.currency_id, opi.currency_name, opi.taxes_type, opi.invoice_type, opi.gst_price, opi.tax_price, opi.barcode_type, opi.warehouse_id, opi.warehouse_code, opi.warehouse_name, opi.soure_no, opi.change_count, opi.item_stat, opi.delivery_date, opi.reply_date, opi.confirm_date, opi.purchase_remark, opi.vendor_remark, opi.make_num, opi.order_num, opi.purchase_confirm, opi.vendor_confirm, opi.purchase_doc_url, opi.vendor_doc_url, opi.receive_num, opi.wait_num, opi.is_main, opi.main_item_id, opi.big_pack_standard_num, opi.small_pack_standard_num, opi.big_pack_label_num, opi.small_pack_label_num, opi.big_pack_mantissa, opi.small_pack_mantissa, opi.pur_employee_name, opi.sale_employee_name, opi.goods_class_name, opi.delivery_stat, opi.matched_plan_num, opi.refund_num, opi.un_competent_num, opi.aux_uom_id, opi.aux_uom_code, opi.aux_uom_name, opi.fix_num, (opi.order_num - IFNULL(opi.matched_plan_num, 0)) can_match_plan_num, (opi.order_num - opi.make_num) can_make_num, opi.erp_master_num, opi.erp_reject_num, op.purchasing_group, op.reserved06, op.vendor_delivery_address, op.shipping_address, opi.mrp_region, opi.return_mark, opi.free_mark, opi.ref_ded_num, opi.ret_ded_num, opi.delete_flag FROM order_pur op, order_pur_item opi WHERE op.id = opi.pur_id AND op.delete_flag = 0 AND opi.delete_flag = 0 AND op.tenant_id = 24739 AND op.dept_id = '28905' ORDER BY op.id DESC, opi.seq ASC LIMIT 20
[INFO ] [2025-08-01 09:38:43.479] - <0><1951095214787207168> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:38:43.497] - <0><1951095214787207168> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 20
[INFO ] [2025-08-01 09:38:54.443] - <0><1951095260773556224> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:38:54.472] - <0><1951095260773556224> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 50
[INFO ] [2025-08-01 09:39:06.273] - <0><1951095310371201024> select * from sys_user where user_code = 'SU20250504628_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-01 09:39:06.285] - <0><1951095310371201024> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30395)
[INFO ] [2025-08-01 09:39:06.303] - <0><1951095310371201024> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29693
[INFO ] [2025-08-01 09:39:07.069] - <0><1951095313697284096> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30395)
[INFO ] [2025-08-01 09:39:07.106] - <0><1951095313697284096> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29693
[INFO ] [2025-08-01 09:39:07.562] - <0><1951095315823796224> select '1' from dual
[INFO ] [2025-08-01 09:39:07.562] - <0><1951095315819601920> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25399 ORDER BY id ASC
[INFO ] [2025-08-01 09:39:07.570] - <0><1951095315798630400> select '1' from dual
[INFO ] [2025-08-01 09:39:07.578] - <0><1951095315823796224> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25399 ORDER BY id ASC
[INFO ] [2025-08-01 09:39:07.584] - <0><1951095315798630400> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25399 AND status = 1) AND tenant_id = 25399 ORDER BY id ASC
[INFO ] [2025-08-01 09:39:07.863] - <0><1951095317065310208> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25399 AND parent_id = 0)
[INFO ] [2025-08-01 09:39:07.876] - <0><1951095317065310208> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25399 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-01 09:39:08.358] - <0><1951095319133102080> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25399 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 09:39:08.365] - <0><1951095319200210944> select count(*) from base_sample bs WHERE bs.vendor_id = 25399 and bs.sample_stat = 1
[INFO ] [2025-08-01 09:39:08.375] - <0><1951095319200210944> select count(*) from base_sample bs WHERE bs.vendor_id = 25399 and bs.sample_stat = 2
[INFO ] [2025-08-01 09:39:08.385] - <0><1951095319200210944> select count(*) from base_sample bs WHERE bs.vendor_id = 25399 and bs.sample_stat = 3
[INFO ] [2025-08-01 09:39:08.395] - <0><1951095319200210944> select count(*) from base_sample bs WHERE bs.vendor_id = 25399 and bs.sample_stat = 4
[INFO ] [2025-08-01 09:39:08.405] - <0><1951095319200210944> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25399 and bsi.item_stat = 1
[INFO ] [2025-08-01 09:39:08.414] - <0><1951095319200210945> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25399 AND bvni.is_read = '0'
[INFO ] [2025-08-01 09:39:08.416] - <0><1951095319200210944> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25399 and bsi.item_stat = 2
[INFO ] [2025-08-01 09:39:08.425] - <0><1951095319200210944> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25399 and bsi.item_stat = 3
[INFO ] [2025-08-01 09:39:08.425] - <0><1951095319133102081> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25399 ORDER BY id DESC LIMIT 10
[INFO ] [2025-08-01 09:39:08.488] - <0><1951095319200210944> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25399 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25399
[INFO ] [2025-08-01 09:39:08.511] - <0><1951095319200210944> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30395 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25399 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25399 AND op.pur_id = 30395
[INFO ] [2025-08-01 09:39:08.533] - <0><1951095319200210944> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25399 AND op.stat = 3
[INFO ] [2025-08-01 09:39:08.558] - <0><1951095319200210944> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25399 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 09:39:08.573] - <0><1951095319200210944> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25399 AND op.stat = 4
[INFO ] [2025-08-01 09:39:08.589] - <0><1951095319200210944> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25399 AND op.stat = 5
[INFO ] [2025-08-01 09:39:08.604] - <0><1951095319200210944> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25399
[INFO ] [2025-08-01 09:39:08.641] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.668] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.687] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.704] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.762] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.783] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.812] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.832] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.850] - <0><1951095319200210944> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25399 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:39:08.872] - <0><1951095319200210944> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25399
[INFO ] [2025-08-01 09:39:08.891] - <0><1951095319200210944> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25399 AND disi.inspection_results = 1 AND dis.tenant_id = 25399
[INFO ] [2025-08-01 09:39:08.913] - <0><1951095319200210944> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25399 AND disi.inspection_results = 2 AND dis.tenant_id = 25399
[INFO ] [2025-08-01 09:39:08.931] - <0><1951095319200210944> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25399 AND disi.inspection_results = 3 AND dis.tenant_id = 25399
[INFO ] [2025-08-01 09:39:08.949] - <0><1951095319200210944> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25399 AND disi.inspection_results = 4 AND dis.tenant_id = 25399
[INFO ] [2025-08-01 09:39:41.529] - <0><1951095457675157504> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND vendor_id = 25399 AND vendor_id = '25399')
[INFO ] [2025-08-01 09:39:44.213] - <0><1951095469415014400> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND vendor_id = 25399 AND vendor_id = '25399')
[INFO ] [2025-08-01 09:39:49.988] - <0><1951095493628731392> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND vendor_id = 25399 AND vendor_id = '25399')
[INFO ] [2025-08-01 09:39:56.467] - <0><1951095520707158016> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND vendor_id = 25399 AND vendor_id = '25399')
[INFO ] [2025-08-01 09:43:49.218] - <0><1951096496751058944> select '1' from dual
[INFO ] [2025-08-01 09:43:49.233] - <0><1951096496751058944> SELECT COUNT(*) FROM order_pur_item opl INNER JOIN order_pur op ON op.id = opl.pur_id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND opl.item_stat = 4 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30395 AND jof.oper_type = 1 AND jof.order_type = 2 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25399 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25399 AND op.dept_id = '28905'
[INFO ] [2025-08-01 09:43:57.687] - <0><1951096532511694848> SELECT COUNT(*) FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25399 AND op.dept_id = '28905'
[INFO ] [2025-08-01 09:44:17.079] - <0><1951096613696643072> SELECT COUNT(*) FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.order_date >= STR_TO_DATE('2025-08-07 00:00:00', '%Y-%m-%d %H:%i:%s') AND op.order_date <= STR_TO_DATE('2025-08-09 23:59:59', '%Y-%m-%d %H:%i:%s') AND op.vendor_id = 25399 AND op.dept_id = '28905'
[INFO ] [2025-08-01 09:44:22.031] - <0><1951096633548283904> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-01 09:44:22.045] - <0><1951096633548283904> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:44:22.066] - <0><1951096633548283904> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:44:22.841] - <0><1951096638182989824> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 09:44:22.885] - <0><1951096638182989824> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 09:44:23.371] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:44:23.384] - <0><1951096640347250688> select '1' from dual
[INFO ] [2025-08-01 09:44:23.391] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-01 09:44:23.400] - <0><1951096640347250688> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 09:44:23.408] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:44:23.423] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-01 09:44:23.442] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:44:23.458] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-01 09:44:23.474] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:44:23.486] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-01 09:44:23.505] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:44:23.524] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-01 09:44:23.540] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:44:23.557] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-01 09:44:23.576] - <0><1951096640334667776> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 09:44:23.672] - <0><1951096641668456448> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-01 09:44:23.688] - <0><1951096641668456448> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-01 09:44:24.230] - <0><1951096643983712257> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-01 09:44:24.230] - <0><1951096643983712256> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 09:44:24.242] - <0><1951096643983712256> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 09:44:24.242] - <0><1951096643983712257> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-01 09:44:24.264] - <0><1951096643983712257> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-01 09:44:24.282] - <0><1951096643983712257> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-01 09:44:24.302] - <0><1951096643983712257> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-01 09:44:24.356] - <0><1951096643983712257> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-01 09:44:24.417] - <0><1951096643983712257> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-01 09:44:24.442] - <0><1951096643983712257> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-01 09:44:24.458] - <0><1951096643983712257> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-01 09:44:24.471] - <0><1951096643983712257> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-01 09:44:24.551] - <0><1951096643983712257> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-01 09:44:24.580] - <0><1951096643983712257> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-01 09:44:24.598] - <0><1951096643983712257> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-01 09:44:24.618] - <0><1951096643983712257> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 09:44:24.637] - <0><1951096643983712257> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-01 09:44:24.655] - <0><1951096643983712257> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-01 09:44:24.673] - <0><1951096643983712257> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-01 09:44:24.716] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.738] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.757] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.776] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.795] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.816] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.836] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.855] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.874] - <0><1951096643983712257> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 09:44:24.897] - <0><1951096643983712257> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-01 09:44:24.910] - <0><1951096643983712257> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:44:24.936] - <0><1951096643983712257> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:44:24.955] - <0><1951096643983712257> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:44:24.973] - <0><1951096643983712257> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 09:44:33.006] - <0><1951096679836622848> SELECT * FROM (SELECT reply_date AS startDate FROM order_pur_item WHERE wait_num > 0 AND item_stat = 4 AND pur_id IN (SELECT id FROM order_pur WHERE tenant_id = 24739) UNION SELECT plan_date AS startDate FROM dm_delivery_plan_item WHERE delete_flag = 0 AND plan_date IS NOT NULL AND sale_id IN (SELECT id FROM order_pur WHERE tenant_id = 24739)) od ORDER BY od.startDate ASC LIMIT 1
[INFO ] [2025-08-01 09:44:33.166] - <0><1951096681224937472> SELECT COUNT(*) FROM (SELECT '2025-08-01' AS startDate, so.pur_id, so.pur_name, so.dept_id, so.dept_name, so.dept_code, soi.order_price_uom, soi.werks, soi.price_uom, so.tenant_name tenantName, soi.delivery_type, so.id saleId, soi.id saleItemId, so.pur_no saleNo, it.plan_no planNo, so.order_date orderDate, soi.goods_id goodsId, soi.goods_code goodsCode, soi.goods_name goodsName, soi.goods_model goodsModel, soi.goods_erp_code goodsErpCode, soi.goods_class_name goodsClassName, soi.class_code goodsClassCode, soi.seq saleSeq, ifnull(it.drawing_no, '-') AS drawingNo, it.match_num AS orderNum, (SELECT IFNULL(SUM(dev_num), 0) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum, so.vendor_id, so.vendor_code vendorCode, so.vendor_name vendorName, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) waitNum, soi.uom_id uomId, soi.uom_code, soi.uom_name uomName, soi.gst_price gstPrice, soi.tax_price taxPrice, soi.gst_price * soi.order_num gstAmount, soi.currency_name currencyName, soi.currency_id currencyId, soi.rate_id rateId, soi.rate_val rateVal, soi.rate_name rateName, soi.taxes_type taxesType, soi.invoice_type invoiceType, so.order_type orderType, soi.purchase_remark remark, soi.change_count changeCount, so.stat, soi.seq, soi.is_close, it.plan_date replyDate, barcode_type barcodeType, it.purchaser_name purEmployee, soi.sale_employee_name saleEmployee, soi.soure_no sourceNo, it.delivery_date deliveryDate, it.warehouse_code warehouseCode, soi.warehouse_name warehouseName, soi.item_stat itemStat, soi.delivery_type deliveryType, it.collect_flag collectFlag, it.receiving_control receivingControl, so.vendor_delivery_address, so.shipping_address, it.id, it.id AS list_id, it.id AS plan_srm_line_id, IFNULL(it.refund_num, 0) refund_num, IFNULL(it.un_competent_num, 0) un_competent_num, soi.tenant_id tenantId, ifnull(it.make_num, 0) makeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) canMakeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) devNum, ifnull(it.make_num, 0) tempFixNum, so.order_type, soi.aux_uom_id, soi.aux_uom_code, soi.aux_uom_name, soi.aux_uom_num, it.uom_num, 1 is_source_to_plan, 0 convert_numerator, (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num FROM order_pur so, order_pur_item soi, dm_delivery_plan_item it WHERE so.id = soi.pur_id AND it.sale_item_id = soi.id AND it.sale_id = so.id AND it.delete_Flag <> 2 AND ((so.change_count = 0 AND item_stat = 4) OR so.change_count > 0 AND item_stat IN (1, 4)) AND soi.delivery_type = 2 AND (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) > 0 AND soi.is_close = 0 AND soi.return_mark = 0 AND so.dept_id = 28905) so WHERE so.canMakeNum > 0
[INFO ] [2025-08-01 09:44:33.209] - <0><1951096681224937472> SELECT * FROM (SELECT '2025-08-01' AS startDate, so.pur_id, so.pur_name, so.dept_id, so.dept_name, so.dept_code, soi.order_price_uom, soi.werks, soi.price_uom, so.tenant_name tenantName, soi.delivery_type, so.id saleId, soi.id saleItemId, so.pur_no saleNo, it.plan_no planNo, so.order_date orderDate, soi.goods_id goodsId, soi.goods_code goodsCode, soi.goods_name goodsName, soi.goods_model goodsModel, soi.goods_erp_code goodsErpCode, soi.goods_class_name goodsClassName, soi.class_code goodsClassCode, soi.seq saleSeq, ifnull(it.drawing_no, '-') AS drawingNo, it.match_num AS orderNum, (SELECT IFNULL(SUM(dev_num), 0) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum, so.vendor_id, so.vendor_code vendorCode, so.vendor_name vendorName, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) waitNum, soi.uom_id uomId, soi.uom_code, soi.uom_name uomName, soi.gst_price gstPrice, soi.tax_price taxPrice, soi.gst_price * soi.order_num gstAmount, soi.currency_name currencyName, soi.currency_id currencyId, soi.rate_id rateId, soi.rate_val rateVal, soi.rate_name rateName, soi.taxes_type taxesType, soi.invoice_type invoiceType, so.order_type orderType, soi.purchase_remark remark, soi.change_count changeCount, so.stat, soi.seq, soi.is_close, it.plan_date replyDate, barcode_type barcodeType, it.purchaser_name purEmployee, soi.sale_employee_name saleEmployee, soi.soure_no sourceNo, it.delivery_date deliveryDate, it.warehouse_code warehouseCode, soi.warehouse_name warehouseName, soi.item_stat itemStat, soi.delivery_type deliveryType, it.collect_flag collectFlag, it.receiving_control receivingControl, so.vendor_delivery_address, so.shipping_address, it.id, it.id AS list_id, it.id AS plan_srm_line_id, IFNULL(it.refund_num, 0) refund_num, IFNULL(it.un_competent_num, 0) un_competent_num, soi.tenant_id tenantId, ifnull(it.make_num, 0) makeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) canMakeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) devNum, ifnull(it.make_num, 0) tempFixNum, so.order_type, soi.aux_uom_id, soi.aux_uom_code, soi.aux_uom_name, soi.aux_uom_num, it.uom_num, 1 is_source_to_plan, 0 convert_numerator, (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num FROM order_pur so, order_pur_item soi, dm_delivery_plan_item it WHERE so.id = soi.pur_id AND it.sale_item_id = soi.id AND it.sale_id = so.id AND it.delete_Flag <> 2 AND ((so.change_count = 0 AND item_stat = 4) OR so.change_count > 0 AND item_stat IN (1, 4)) AND soi.delivery_type = 2 AND (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) > 0 AND soi.is_close = 0 AND soi.return_mark = 0 AND so.dept_id = 28905) so WHERE so.canMakeNum > 0 ORDER BY so.deliveryDate ASC LIMIT 20
[INFO ] [2025-08-01 09:44:33.226] - <0><1951096681224937472> select * from base_config where param_key = 'deliveryHomeDays' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-01 09:44:33.297] - <0><1951096681224937472> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1066065, 1078086))
[INFO ] [2025-08-01 09:44:42.829] - <0><1951096721884520448> SELECT COUNT(*) FROM order_pur op WHERE op.delete_flag = 0 AND op.stat > 2 AND op.dept_id = '28905' AND op.tenant_id = 24739
[INFO ] [2025-08-01 09:44:42.863] - <0><1951096721884520448> SELECT op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name, op.dept_id, op.dept_code, op.dept_name, op.order_date, op.dep_code, op.bsart, op.order_flag, op.sync_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.tenant_name, op.currency_name, op.total_amount, op.order_remark, op.publish_date, op.pur_name, op.reserved06, op.purchasing_group, opi.item_stat, opi.is_close, opi.erp_change_type, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model, opi.delivery_stat, opi.order_num, opi.matched_plan_num, opi.make_num, opi.fix_num, opi.wait_num, opi.receive_num, opi.refund_num, opi.ref_ded_num, opi.erp_master_num, opi.erp_reject_num, opi.ret_ded_num, opi.uom_name, opi.rate_name, opi.delivery_date, opi.tax_price, opi.gst_price, opi.gst_amount, opi.source_id FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND op.stat > 2 AND op.dept_id = '28905' AND op.tenant_id = 24739 ORDER BY op.id DESC LIMIT 20
[INFO ] [2025-08-01 09:49:51.737] - <0><1951098017630855168> select '1' from dual
[INFO ] [2025-08-01 09:49:51.748] - <0><1951098017630855168> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:49:51.765] - <0><1951098017630855168> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 20
[INFO ] [2025-08-01 09:50:13.856] - <0><1951098110421442560> SELECT COUNT(*) FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905')
[INFO ] [2025-08-01 09:50:13.876] - <0><1951098110421442560> SELECT id, tenant_p_id, tenant_id, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, goods_id, goods_erp_code, goods_code, goods_name, goods_model, drawing_no, plan_date, plan_num, reply_qty, shortage_num, is_match, remark, created_by_name, last_updated_by_name, creation_date, created_by, last_update_date, last_updated_by, last_update_login, delete_flag, version_num, source_no, plan_no, plan_id, plan_line_id, warehouse_id, warehouse_code, warehouse_name, collect_flag, receiving_control, address, order_type, purchaser_id, purchaser_name, purchasing_group, mrp_region, erp_source_type, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_plan WHERE (tenant_id = 24739 AND dept_id = '28905') ORDER BY plan_date ASC LIMIT 20
[INFO ] [2025-08-01 10:06:51.021] - <0><1951102290020212736> select '1' from dual
[INFO ] [2025-08-01 10:06:52.379] - <0><1951102297905504256> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-01 10:06:52.793] - <0><1951102297905504256> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-01 10:06:54.045] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 10:06:54.081] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-01 10:06:54.116] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 10:06:54.177] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-01 10:06:54.205] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 10:06:54.223] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-01 10:06:54.274] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 10:06:54.363] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-01 10:06:54.456] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 10:06:54.625] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-01 10:06:54.765] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 10:06:54.790] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-01 10:06:54.806] - <0><1951102305379753986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-01 10:07:13.242] - <0><1951102305375559680> select '1' from dual
[INFO ] [2025-08-01 10:07:13.267] - <0><1951102305375559680> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-01 10:07:13.369] - <0><1951102306260557824> select '1' from dual
[INFO ] [2025-08-01 10:07:13.383] - <0><1951102306260557824> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-01 10:07:13.404] - <0><1951102306260557824> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-01 10:07:13.407] - select '1' from dual
[INFO ] [2025-08-01 10:07:14.073] - <0><1951102389534269440> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-01 10:07:14.075] - <0><1951102389534269441> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-01 10:07:14.092] - <0><1951102389534269440> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-01 10:07:14.094] - <0><1951102389534269441> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-01 10:07:14.113] - <0><1951102389534269440> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-01 10:07:14.130] - <0><1951102389534269440> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-01 10:07:14.146] - <0><1951102389534269440> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-01 10:07:14.157] - <0><1951102389534269440> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-01 10:07:14.171] - <0><1951102389534269440> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-01 10:07:14.185] - <0><1951102389534269440> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-01 10:07:14.198] - <0><1951102389534269440> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-01 10:07:14.213] - <0><1951102389534269440> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-01 10:07:14.290] - <0><1951102389534269440> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-01 10:07:14.322] - <0><1951102389534269440> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-01 10:07:14.345] - <0><1951102389534269440> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-01 10:07:14.363] - <0><1951102389534269440> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-01 10:07:14.385] - <0><1951102389534269440> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-01 10:07:14.407] - <0><1951102389534269440> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-01 10:07:14.429] - <0><1951102389534269440> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-01 10:07:14.470] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.498] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.527] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.556] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.580] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.611] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.642] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.679] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.712] - <0><1951102389534269440> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-01 10:07:14.742] - <0><1951102389534269440> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-01 10:07:14.769] - <0><1951102389534269440> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 10:07:14.794] - <0><1951102389534269440> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 10:07:14.818] - <0><1951102389534269440> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-01 10:07:14.840] - <0><1951102389534269440> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
