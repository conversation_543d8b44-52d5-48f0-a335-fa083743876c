<template>
  <div class="DIAN-common-layout">
    <div class="DIAN-common-layout-center">

      <!-- 搜索框 -->
      <el-row class="DIAN-common-search-box" :gutter="24">
        <el-form @submit.native.prevent>
          <el-col :span="5">
            <el-form-item>
              <el-date-picker
                v-model="queryParam.orderDate"
                type="daterange"
                placeholder="请输入订单日期"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.dept" placeholder="采购组织" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.orderNo" placeholder="订单号" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.goods" placeholder="物料编码/名称/规格" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
<!--          <el-col :span="5">-->
<!--            <el-form-item>-->
<!--              <el-select v-model="queryParam.bsart" placeholder="请选择采购类型" clearable>-->
<!--                <el-option-->
<!--                  v-for="item in bsartTypeOptions"-->
<!--                  :key="item.key"-->
<!--                  :label="item.value"-->
<!--                  :value="item.key"-->
<!--                />-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->

          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="search()">{{$t('common.search')}}</el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()">{{$t('common.reset')}}</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <!-- body -->
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <!-- 表头工具栏 -->
        <div class="DIAN-common-head">
          <div>
            <template>
              <!--              <el-button type="primary" @click="exportHandle()" v-has-per="'order:pur:export'" icon="el-icon-top">{{ $t('common.exportBtn') }}</el-button>-->
              <el-button type="primary" @click="exportHandle()" icon="el-icon-top">{{ $t('common.exportBtn') }}</el-button>
            </template>
          </div>
          <div class="DIAN-common-head-right">
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false" @click="search()" />
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>

        <!-- 表格 -->
        <d-table ref="listTable" v-loading="listLoading" :data="list" hasC show-summary>
          <el-table-column prop="deptName" label="采购组织" align="center" show-overflow-tooltip width="180"/>
          <el-table-column prop="purNo" label="订单号/序号" align="center" width="150">
            <template slot-scope="scope">
              <span>{{scope.row.purNo + '/' + scope.row.seq}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderType" label="订单类型" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{scope.row.orderType | commonEnumsTurn("common.JinDieOrderTypeEnum")}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="purName" label="采购员" align="center" show-overflow-tooltip width="90"/>
          <el-table-column prop="goodsErpCode" label="物料编码" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="goodsName" label="物料名称" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="goodsModel" label="物料描述" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="deliveryDate" label="送货日期" align="center" show-overflow-tooltip width="120">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.deliveryDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishStatus" label="完成状态" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ (scope.row.lineOrderSum === (scope.row.erpMasterNum - scope.row.erpRejectNum)) | commonEnumsTurn("base.SampleCompleteSignEnum") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="lineOrderSum" label="订单数量" align="center" show-overflow-tooltip width="90"/>
          <el-table-column prop="makeNum" label="已制单量" align="center" show-overflow-tooltip width="100"/>
          <!-- <el-table-column prop="matchedPlanNum" label="已匹配计划量" align="center" show-overflow-tooltip width="100"/> -->
          <el-table-column prop="canMakeNum" label="未制单量" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="fixNum" label="已送货量" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="waitNum" label="待送货量" align="center" show-overflow-tooltip width="100"/>
          <!-- <el-table-column prop="unCompetentNum" label="不合格数" align="center" show-overflow-tooltip width="100"/> -->
          <el-table-column prop="refundNum" label="暂退补料数量" width="100"/>
          <el-table-column prop="refDedNum" label="暂退扣款数量" width="100"/>
          <el-table-column prop="erpMasterNum" label="入库数量" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="erpRejectNum" label="退货补料数量" width="100"/>
          <el-table-column prop="retDedNum" label="退货扣款数量" width="100"/>
          <el-table-column prop="gstPrice" label="含税单价" align="center" show-overflow-tooltip width="100" v-if="$dian.hasPerBtnP('order:vendor:lookPrice')"/>
          <el-table-column prop="gstAmount" label="含税金额" align="center" show-overflow-tooltip width="100" v-if="$dian.hasPerBtnP('order:vendor:lookPrice')"/>
          <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip/>
          <el-table-column label="操作" width="100" fixed="right" align="center">
            <template slot-scope="scope">
              <template>
                <el-button type="text" size="mini" @click="addEditOrderHandle(scope.row.id)" v-has-per="'order:sale:info'"> {{ $t('common.lookBtn')}} </el-button>
              </template>
            </template>
          </el-table-column>
        </d-table>
        <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initData"/>
        <CodePrintTemplate v-if="templateVisible" ref="PrintTemplate"/>
      </div>

      <!-- FORM表单 -->
      <Form ref="form" v-show="formVisible" @callRefreshList="closeForm"></Form>
      <!-- 下载/导出 组件 -->
      <d-export ref="export" title="订单执行进度表导出"></d-export>

    </div>
  </div>
</template>

<script>

import {dFlowMixin} from "@dian/dian-ui-vue";
import Form from '@/views/order/produce/vendor/Form';
import store from "@/store";
import {getVendorSaleOrderProgressList} from "@/api/order/sale";
import dian from "@/utils/dian";
import CodePrintTemplate from './codePrintTemplate'

export default {
  //加载底层公有组件
  mixins: [dFlowMixin],
  name: "order-progress-vendor",
  components: {
    Form,
    CodePrintTemplate,
  },
  data() {
    return {
      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型
      queryParam: {
        page: 1,
        limit: 20,
        keyword : '',  // 订单号/物料编码/物料名称/物料描述
        orderDate : '',  // 订单日期
        dept:'',
        orderNo:'',
        goods:'',
        bsart : '', // 采购类型
      },
      formVisible: false,
      listLoading: false,
      btnLoading: false,
      templateVisible: false,
      list: [],
      total: 0
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.listLoading = true;
      if (this.queryParam.orderDate.length !== 0) {
        const startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');
        const endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');
        this.queryParam.orderDate = startDate +" 至 "+endDate;
      }
      let subCompanyInfoData = dian.storageGet('subCompanyInfo');
      if (subCompanyInfoData){
        this.queryParam.deptId = subCompanyInfoData.id;
      }
      getVendorSaleOrderProgressList(this.queryParam).then(res => {
        this.total = res.page.totalCount;
        this.list = res.page.list;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      })
    },
    // 新增|编辑 项目报备
    addEditOrderHandle(id) {
      this.formVisible = true;
      this.$nextTick(() => {
        this.$refs.form.init(id);
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedDatas = selection.map(item => item)
      //获取所有选中项数组的长度
      this.selectedNum = selection.length
    },
    // 导出
    exportHandle() {
      // 当导出为全部时，赋予总数为最大值
      this.queryParam.total = this.total;
      this.$refs.export.init('/api/order/sale/saleProgressExport', '订单执行进度表',this.queryParam);
    },
    // 搜索方法，并返回到第一页
    search() {
      this.queryParam.page = 1;
      this.initData();
    },
    // 重置方法
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.search();
    },
    // Form表单关闭时回调方法
    closeForm() {
      this.formVisible = false;
      this.initData();
    },
    // 打印条码
    printBarCode(data) {
      this.templateVisible = true
      let params = {
        id: null,
        tenantPId: 0,
        templateName: '',
        sceneId: null,
        sceneCode: '',
        sceneName: '',
        remark: '',
        printJson: '',
        goodsName:data.goodsName,
        goodsCode:data.goodsErpCode,
        vendorName:data.vendorName,
        vendorCode:data.vendorCode,
        devSumNum: 0, // 总送货数
        PackagingNum: 0, // 每箱数量
        numberOfBoxes: 0, // 箱数
        countless: 0, // 尾数
        prodDate: null, // 生产日期
        warrantyPeriod: '', // 质保天数
        warrantyDate: '', // 质保日期
        prodBatchNo: '', // 批次号
      }
      this.$nextTick(() => {
        this.$refs.PrintTemplate.goodsCode=data.goodsCode;
        this.$refs.PrintTemplate.isBatchPrint = false;
        this.$refs.PrintTemplate.printData = params;
        this.$refs.PrintTemplate.dataForm = params;
        this.$refs.PrintTemplate.init("deliveryNotePrinting")
      })
    },
  }
}
</script>

<style scoped>

</style>
