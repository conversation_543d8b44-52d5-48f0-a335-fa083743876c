<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dian.modules.order.dao.PurItemDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dian.modules.order.entity.PurItemEntity" id="purItemMap">
        <result property="tenantPId" column="tenant_p_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="purId" column="pur_id"/>
        <result property="seq" column="seq"/>
        <result property="id" column="id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="goodsErpCode" column="goods_erp_code"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="goodsName" column="goods_name"/>
        <result property="goodsModel" column="goods_model"/>
        <result property="uomId" column="uom_id"/>
        <result property="uomName" column="uom_name"/>
        <result property="rateId" column="rate_id"/>
        <result property="rateName" column="rate_name"/>
        <result property="rateVal" column="rate_val"/>
        <result property="currencyId" column="currency_id"/>
        <result property="currencyName" column="currency_name"/>
        <result property="taxesType" column="taxes_type"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="gstPrice" column="gst_price"/>
        <result property="taxPrice" column="tax_price"/>
        <result property="barcodeType" column="barcode_type"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="soureNo" column="soure_no"/>
        <result property="changeCount" column="change_count"/>
        <result property="itemStat" column="item_stat"/>
        <result property="deliveryDate" column="delivery_date"/>
        <result property="replyDate" column="reply_date"/>
        <result property="purchaseRemark" column="purchase_remark"/>
        <result property="vendorRemark" column="vendor_remark"/>
        <result property="makeNum" column="make_num"/>
        <result property="orderNum" column="order_num"/>
        <result property="purchaseConfirm" column="purchase_confirm"/>
        <result property="vendorConfirm" column="vendor_confirm"/>
        <result property="purchaseDocUrl" column="purchase_doc_url"/>
        <result property="vendorDocUrl" column="vendor_doc_url"/>
        <result property="fixNum" column="fix_num"/>
        <result property="waitNum" column="wait_num"/>
        <result property="creationDate" column="creation_date"/>
        <result property="isMain" column="is_main"/>
        <result property="mainItemId" column="main_item_id"/>
        <result property="bigPackStandardNum" column="big_pack_standard_num"/>
        <result property="smallPackStandardNum" column="small_pack_standard_num"/>
        <result property="bigPackLabelNum" column="big_pack_label_num"/>
        <result property="smallPackLabelNum" column="small_pack_label_num"/>
        <result property="bigPackMantissa" column="big_pack_mantissa"/>
        <result property="smallPackMantissa" column="small_pack_mantissa"/>
        <result property="purEmployeeName" column="pur_employee_name"/>
        <result property="saleEmployeeName" column="sale_employee_name"/>
        <result property="goodsClassName" column="goods_class_name"/>
        <result property="deliveryStat" column="delivery_stat"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createId" column="create_id"/>
        <result property="creater" column="creater"/>
        <result property="createDate" column="create_date"/>
        <result property="modifiId" column="modifi_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyDate" column="modify_date"/>
    </resultMap>

    <select id="findPagination" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        po.id,
        poi.id childId,
        po.pur_no purNo,
        po.dep_code depCode,
        po.tenant_name tenantName,
        po.vendor_id vendorId,
        po.vendor_code vendorCode,
        po.vendor_name vendorName,
        po.order_date orderDate,
        poi.goods_code goodsCode,
        poi.goods_name goodsName,
        poi.goods_model goodsModel,
        poi.order_num orderNum,
        poi.uom_name uomName,
        poi.gst_price gstPrice,
        IFNULL(poi.gst_amount,poi.gst_price*order_num) gstAmount,
        poi.currency_name currencyName,
        poi.rate_val rateVal,
        poi.rate_name rateName,
        po.order_type orderType,
        poi.line_remark lineRemark,
        poi.sign,
        poi.vendor_remark vendorRemark,
        poi.change_count changeCount,
        po.vendor_delivery_address,
        po.shipping_address,
        (CASE WHEN stat=1 THEN '制单' WHEN stat=2 THEN '审核中' WHEN stat=3 THEN '待答交'
        WHEN stat=4 THEN '答交异常' WHEN stat=5 THEN '已确认' WHEN  stat=0 THEN '已作废' END) stat,
        poi.seq,
        DATE_FORMAT(poi.reply_date,'%Y-%m-%d') replyDate,
<!--        \n" +//答交日期-->
       barcode_type barcodeType,
<!--        \n"+//条码类型-->
        poi.pur_employee_name purEmployye,
<!--        \n"+//采购方业务员-->
        poi.sale_employee_name saleEmployee,
<!--        \n"+//销售方业务员-->
        poi.soure_no sourceNo,
<!--        \n"+//来源单号-->
        DATE_FORMAT(poi.delivery_date,'%Y-%m-%d') deliveryDate,
<!--        \n" +//订单货期-->
        poi.purchase_remark purchaseRemark,
<!--        \n" +//备注-->
        (CASE poi.item_stat WHEN 0 THEN '作废' when -1 then '待发布'  WHEN 1 THEN '待答交'  WHEN 2 THEN '答交异常' WHEN 3 THEN '供方拒绝' when 4 then '已确认' END) itemStat,
<!--        \n" +  //行状态;1-待答复;2-交易异常;3-供方拒绝,4-已确认;-->
        poi.creater createdByName,
<!--        \n"+  //采购方业务员-->
        poi.warehouse_code warehouseCode,
<!--        \n"+//仓库编码-->
        poi.warehouse_name warehouseName,
<!--        \n"+//仓库名称-->
        poi.create_date creationDate,
<!--        \n"+//创建(下载时间)-->
<!--    		IFNULL((SELECT id FROM bas_doc bd WHERE bd.soure_item_id=poi.id AND bill_type=3 AND delete_flag=0 limit 0,1),0) isUpload,\n"+//明细是否上传文件-->
<!--      		IFNULL((SELECT file_path FROM bas_doc bd WHERE bd.soure_item_id=poi.id AND bill_type=3 AND delete_flag=0 limit 0,1),'') filePath\n"+//明细是否上传文件-->
        poi.purchase_doc_url purchaseDocUrl,
<!--        \n"+//采购方文档地址-->
        poi.vendor_doc_url vendorDocUrl,
<!--        \n"+//供应方文档地址-->
        poi.purchase_doc_name purchaseDocName,
<!--        \n"+//采购方文档名称-->
        poi.vendor_doc_name vendorDocName,
<!--        \n"+//供应方方文档名称-->
        poi.fix_num fixNum,
<!--        \n"+//已送货量-->
        poi.wait_num waitNum,
<!--        \n"+//待送货量-->
        (CASE poi.vendor_confirm WHEN 1 THEN '待确认'  WHEN 2 THEN '已确认' else '无' END) vendorConfirm,
<!--        \n"+//供应方确认-->
        (CASE poi.purchase_confirm WHEN 1 THEN '待确认'  WHEN 2 THEN '已确认' else '无' END) purchaseConfirm,
<!--        \n"+//采购方确认-->
        po.create_id createdBy,
<!--        "+//创建人-->
        poi.goods_class_name goodsClassName
<!--        "+//物料分类-->
         FROM
        order_pur po left join order_pur_item poi
        on po.id=poi.pur_id and po.delete_flag=0 and poi.delete_flag=0
<!--        "+//是否关注-->
        <where>

        <if test="params.searchStr !=null and params.searchStr != ''">
            and (po.pur_no like CONCAT('%',#{params.searchStr},'%')
            or po.vendor_code like CONCAT('%',#{params.searchStr},'%')
            or po.vendor_name like CONCAT('%',#{params.searchStr},'%')
            or poi.goods_code like CONCAT('%',#{params.searchStr},'%')
            or poi.goods_name like CONCAT('%',#{params.searchStr},'%')
            or poi.goods_model like CONCAT('%',#{params.searchStr},'%'))
        </if>
        <if test="params.startDate != null and params.startDate != ''">
            <![CDATA[
                    and po.order_date >=TO_DATE(#{params.startDate},'yyyy-mm-dd hh24:mi:ss')
                ]]>
        </if>
            <if test="params.endDate != null and params.endDate != ''">
                <![CDATA[
                    and po.order_date <=TO_DATE(#{params.endDate},'yyyy-mm-dd hh24:mi:ss')
                ]]>
            </if>
        <if test="params.purId !=null and params.purId != ''">
           and po.id = #{params.purId}
        </if>
        <if test="params.tenantId !=null and params.tenantId != ''">
           and po.tenant_id = #{params.tenantId}
        </if>
        <if test="params.orderConfirm !=null and params.orderConfirm != ''">
           and poi.item_stat in(1,2,3,4)
        </if>
        <if test="params.waitFb !=null and params.waitFb != ''">
            and  po.stat in (1,2)
        </if>
        <if test="params.noWaitFb !=null and params.noWaitFb != ''">
            and  po.order_stat>2
        </if>
        <if test="params.myOrder !=null and params.myOrder != ''">
            and  po.create_id = #{params.userId}
        </if>
        <if test="params.orderStats !=null and params.orderStats != ''">
            and poi.item_stat =#{params.orderStats} and po.stat>2
        </if>
        <if test="params.myFollow !=null and params.myFollow != ''">
            and (select count(1) from order_follow jof where jof.follow_id= #{params.userId} and jof.order_type=1 and jof.order_id=po.id and jof.delete_flag=0)>0
        </if>
        <if test="params.waitSend !=null and params.waitSend != ''">
            and poi.item_stat=4 and fix_num=0
        </if>
<!--        急单-->
        <if test="params.busyOrder !=null and params.busyOrder != ''">
            and (datediff(DATE_FORMAT(delivery_date,'%Y-%m-%d'),DATE_FORMAT(order_date,'%Y-%m-%d')) between 0 and 3)
        </if>
<!--        超时未送货-->
        <if test="params.overWaitSend !=null and params.overWaitSend != ''">
            and poi.item_stat=4 and fix_num=0 and now()>poi.reply_date
        </if>
<!--        未完成-->
        <if test="params.noFinish !=null and params.noFinish != ''">
            and poi.wait_num>0
        </if>
<!--        已完成-->
        <if test="params.finish !=null and params.finish != ''">
            and poi.wait_num=0
        </if>
        <if test="params.noReply !=null and params.noReply != ''">
            and poi.item_stat=1 and po.order_stat>2
        </if>
        <if test="params.overNoreply !=null and params.overNoreply != ''">
            and po.reply_stat=1 and now()>poi.delivery_date
        </if>
        <if test="params.purNo !=null and params.purNo != ''">
            and po.pur_no=#{params.purNo}
        </if>
        <if test="params.seq !=null and params.seq != ''">
            and poi.seq=#{params.seq}
        </if>
        </where>
        ORDER BY po.id DESC,poi.seq DESC

    </select>

    <select id="findList" resultType="com.dian.modules.order.entity.PurItemEntity" parameterType="java.util.HashMap">
              select * from order_pur_item p
              <where>
                  <if test="params.purId !=null and params.purId != ''">
                     and p.pur_id = #{params.purId}
                  </if>
                  <if test="params.deleteFlag !=null and params.deleteFlag != ''">
                      and p.delete_flag = #{params.deleteFlag}
                  </if>
                  <if test="params.itemStat !=null and params.itemStat != ''">
                      and p.item_stat = #{params.itemStat}
                  </if>
              </where>

    </select>

    <select id="getPriceList" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        SELECT a.*,po.vendor_id,po.vendor_name,po.pur_no,po.vendor_code FROM order_pur po,order_pur_item a INNER JOIN(
         SELECT goods_id,MAX(poi.id) AS id  FROM order_pur po,order_pur_item poi WHERE po.id=poi.pur_id GROUP BY poi.goods_id,
         poi.gst_price
        ) b ON a.id=b.id WHERE po.id=a.pur_id AND po.delete_flag=0 AND a.delete_flag=0 AND a.item_stat=4 AND po.order_type &lt;&gt; 6
        <if test="params.tenantId !=null and params.tenantId != ''">
            and a.tenant_id = #{params.tenantId}
        </if>
        <if test="params.id !=null and params.id != ''">
            and a.goods_id = #{params.id}
        </if>
        <if test="params.vendorId !=null and params.vendorId != ''">
            and a.vendor_Id = #{params.vendorId}
        </if>
        order by a.create_date,pur_id,seq desc
    </select>

    <select id="getAllPrice" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        SELECT MIN(tax_price) as minTaxPrice,AVG(tax_price) as avgTaxPrice,MAX(tax_price) as maxTaxPrice from  (SELECT a.* FROM order_pur po,order_pur_item a INNER JOIN(
       SELECT goods_id,MAX(poi.id) AS id  FROM order_pur po,order_pur_item poi WHERE po.id=poi.pur_id GROUP BY poi.goods_id,
       poi.gst_price
        ) b ON a.id=b.id WHERE po.id=a.pur_id AND po.delete_flag=0 AND a.delete_flag=0 AND a.item_stat=4 AND po.order_type &lt;&gt;6

        <if test="params.tenantId !=null and params.tenantId != ''">
            and a.tenant_id = #{params.tenantId}
        </if>
        <if test="params.id !=null and params.id != ''">
            and a.goods_id = #{params.id}
        </if>
        <if test="params.vendorId !=null and params.vendorId != ''">
            and po.vendor_Id = #{params.vendorId}
        </if>
        ) aa group by goods_id
    </select>

    <select id="getRateList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT rate_name,rate_val FROM order_pur_item poi,order_pur po WHERE poi.pur_id=po.id AND  po.vendor_id=#{params.vendorId}
        <if test="params.rateName !=null and params.rateName != ''">
            and (rate_name  like  CONCAT('%',#{params.rateName},'%') or rate_val like CONCAT('%',#{params.rateName},'%'))
        </if>
        GROUP BY rate_name,rate_val
    </select>

    <select id="getCurrencyList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT  currency_code,currency_name FROM order_pur WHERE  vendor_id=#{params.vendorId}
        <if test="params.currencyName !=null and params.currencyName != ''">
            and (currency_code  like  CONCAT('%',#{params.currencyName},'%') or currency_name like CONCAT('%',#{params.currencyName},'%'))
        </if>
        GROUP BY currency_code,currency_name
    </select>
    <select id="getOrderSortPlannedNumber" resultType="java.util.HashMap">
        SELECT
        soi.*,
        so.pur_no,
        so.order_type,
        so.vendor_code,
        so.vendor_id,
        so.vendor_name,
        soi.source_item_id,
        so.source_id,
        <!--算有没有超 -->
        ( ifnull( soi.small_set_number, 0 )- soi.order_num ) as super_number ,
        <!--齐套数量 -->
        (soi.small_set_number -
        <!--查询计划没有关闭的计划数量 -->
        ((SELECT IFNULL( SUM( match_num ), 0 )FROM dm_delivery_plan_item dpi WHERE dpi.sale_item_id = soi.id AND dpi.delete_flag &lt;&gt;2)
        <!--查询计划关闭的计划的送货数量减去关闭数的不合格数量 -->
        + (SELECT ( IFNULL( SUM( make_num ), 0 )- IFNULL( SUM( un_competent_num ), 0 ))FROM dm_delivery_plan_item dpi WHERE  dpi.sale_item_id = soi.id AND dpi.delete_flag = 2)
        )) canMatchNum
        FROM
        order_pur_item soi,
        order_pur so
        WHERE
        soi.pur_id = so.id
        AND is_close = 0
        AND soi.tenant_Id = 254
        AND soi.item_stat = 4
        AND reply_date IS NOT NULL
        and   (soi.small_set_number - ((
        SELECT IFNULL(SUM(match_num), 0)
        FROM dm_delivery_plan_item dpi
        WHERE dpi.sale_item_id = soi.id AND dpi.delete_flag  &lt;&gt; 2) + (
        SELECT (IFNULL(SUM(make_num), 0)- IFNULL(SUM(un_competent_num), 0))
        FROM dm_delivery_plan_item dpi
        WHERE dpi.sale_item_id = soi.id AND dpi.delete_flag = 2)))&gt;0
        and goods_id=#{params.goodsId}
        <if test="params.vendorCode != null and params.vendorCode != '' ">
            and vendor_code=#{params.vendorCode}
        </if>
        order by reply_date, (soi.small_set_number - ((
        SELECT IFNULL(SUM(match_num), 0)
        FROM dm_delivery_plan_item dpi
        WHERE dpi.sale_item_id = soi.id AND dpi.delete_flag &lt;&gt;2) + (
        SELECT (IFNULL(SUM(make_num), 0)- IFNULL(SUM(un_competent_num), 0))
        FROM dm_delivery_plan_item dpi
        WHERE dpi.sale_item_id = soi.id AND dpi.delete_flag = 2
        and  dpi.tenant_Id = 254
        )))
    </select>

    <!--根据主表id计算总含税金额-->
    <select id="getOrderItemSumGstAmount" resultType="java.math.BigDecimal">
        select
          IFNULL(SUM(opt.gst_amount),0) total_gst_amount
        from
          order_pur_item opt
        where
          opt.delete_flag = 0
          and opt.pur_id = #{params.purId}
    </select>


    <!-- 获取主从明细数据 -->
    <select id="getOrderItemLine" resultType="java.util.HashMap">
        SELECT
            op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name,op.dept_code, op.dept_name, op.order_date, op.order_flag, op.sync_date, op.publish_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.delete_flag, op.create_id, op.creater, op.create_date, op.modifi_id, op.modifier, op.modify_date, op.dep_code, op.tenant_name, op.is_print, op.print_count, op.source_no, op.order_remark, op.order_confirm_synchronize, op.confirm_date, op.order_reply_date, op.ex_rate, op.bukrs, op.currency_code, op.bsart, op.currency_name, op.total_amount, op.pur_id, op.pur_name, op.pay_text, op.source_id, op.pay_name, op.erp_approve_date,
            opi.pur_id, opi.seq, opi.id AS lineId, opi.goods_id, opi.goods_erp_code, opi.goods_code,
            opi.goods_name, opi.goods_model, opi.uom_id, opi.uom_code, opi.uom_name, opi.rate_id,
            opi.rate_name, opi.rate_val, opi.currency_id, opi.currency_name, opi.taxes_type,
            opi.invoice_type, opi.gst_price, opi.tax_price, opi.barcode_type, opi.warehouse_id,
            opi.warehouse_code, opi.warehouse_name, opi.soure_no, opi.change_count, opi.item_stat,
            opi.delivery_date, opi.reply_date, opi.confirm_date, opi.purchase_remark, opi.vendor_remark,
            opi.make_num, opi.order_num, opi.purchase_confirm, opi.vendor_confirm, opi.purchase_doc_url,
            opi.vendor_doc_url, opi.receive_num, opi.wait_num, opi.is_main, opi.main_item_id, opi.big_pack_standard_num,
            opi.small_pack_standard_num, opi.big_pack_label_num, opi.small_pack_label_num, opi.big_pack_mantissa,
            opi.small_pack_mantissa, opi.pur_employee_name, opi.sale_employee_name, opi.goods_class_name,
            opi.delivery_stat,opi.matched_plan_num,opi.refund_num,opi.un_competent_num,opi.aux_uom_id,opi.aux_uom_code,opi.aux_uom_name,
            opi.fix_num,(opi.order_num - IFNULL(opi.matched_plan_num,0)) can_match_plan_num,(opi.order_num - opi.make_num) can_make_num,
            opi.erp_master_num,opi.erp_reject_num,op.purchasing_group,op.reserved06,op.vendor_delivery_address,op.shipping_address,
            opi.mrp_region,opi.return_mark,opi.free_mark,opi.ref_ded_num,opi.ret_ded_num,opi.delete_flag
        FROM
            order_pur op,order_pur_item opi
        <where>
            op.id = opi.pur_id AND op.delete_flag = 0 AND opi.delete_flag = 0
            <!-- 日期区间 -->
            <if test="params.startDate != null and params.endDate != null and params.startDate != '' and params.endDate != ''">
                AND op.order_date BETWEEN #{params.startDate} AND #{params.endDate}
            </if>
            <!-- 租户(采购方进入) -->
            <if test="params.tenantId != null and params.tenantId != ''">
                AND op.tenant_id = #{params.tenantId}
            </if>
            <!-- 供应商(供应商进入) -->
            <if test="params.vendorId != null and params.vendorId != ''">
                AND op.vendor_id = #{params.vendorId}
            </if>
            <!-- 订单号搜索 -->
            <if test="params.purNo != null and params.purNo != ''">
                AND op.pur_no LIKE CONCAT('%',#{params.purNo},'%')
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                AND op.pur_no LIKE CONCAT('%',#{params.orderNo},'%')
            </if>
            <if test="params.dept != null and params.dept != ''">
                AND (op.dept_code LIKE CONCAT('%',#{params.dept},'%') OR op.dept_name LIKE CONCAT('%',#{params.dept},'%'))
            </if>
            <if test="params.vendor != null and params.vendor != ''">
                AND (op.vendor_code LIKE CONCAT('%',#{params.vendor},'%') OR op.vendor_name LIKE CONCAT('%',#{params.vendor},'%'))
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                AND op.dept_id = #{params.deptId}
            </if>
            <if test="params.purName != null and params.purName != ''">
                AND op.pur_name LIKE CONCAT('%',#{params.purName},'%')
            </if>
            <!-- 供应商搜索 -->
            <if test="params.vendor != null and params.vendor != ''">
                AND (
                    op.vendor_code LIKE CONCAT('%',#{params.vendor},'%') OR
                    op.vendor_name LIKE CONCAT('%',#{params.vendor},'%')
                )
            </if>
            <!-- 产品信息搜索 -->
            <if test="params.goods != null and params.goods != ''">
                AND (
                    opi.goods_erp_code LIKE CONCAT('%',#{params.goods},'%')
                    OR opi.goods_name LIKE CONCAT('%',#{params.goods},'%')
                    or opi.goods_model LIKE CONCAT('%',#{params.goods},'%')
                    or opi.goods_code LIKE CONCAT('%',#{params.goods},'%')
                )
            </if>
            <!-- 单据状态筛选 -->
            <if test="params.itemStat != null and params.itemStat != ''">
                AND opi.item_stat = #{params.itemStat}
            </if>
            <if test="params.returnMark != null and params.returnMark != ''">
                AND opi.return_mark = #{params.returnMark}
            </if>
            <if test="params.freeMark != null and params.freeMark != ''">
                AND opi.free_mark = #{params.freeMark}
            </if>
        </where>
        ORDER BY op.id DESC,opi.seq ASC
    </select>

    <select id="queryCreateReturnMasterList" resultType="java.util.HashMap">
        SELECT
            op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name,
            op.dept_name, op.order_date, op.order_flag, op.sync_date,
            op.publish_date, op.order_type, op.stat, op.reply_stat,
            op.change_count, op.remark, op.delete_flag,
            op.create_id, op.creater, op.create_date, op.modifi_id,
            op.modifier, op.modify_date, op.dep_code, op.tenant_name,
            op.is_print, op.print_count, op.source_no, op.order_remark,
            op.order_confirm_synchronize, op.confirm_date, op.order_reply_date,
            op.ex_rate, op.bukrs, op.currency_code, op.bsart, op.currency_name,
            op.total_amount, op.pur_id, op.pur_name, op.pay_text, op.source_id,
            op.pay_name, op.erp_approve_date,
            opi.tenant_p_id, opi.tenant_id, opi.pur_id, opi.seq, opi.id item_id,
            opi.goods_id, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model,
            opi.uom_id, opi.uom_code, opi.uom_name, opi.rate_id, opi.rate_name, opi.rate_val,
            opi.currency_id, opi.currency_name, opi.taxes_type, opi.invoice_type, opi.gst_price,
            opi.tax_price, opi.barcode_type, opi.warehouse_id, opi.warehouse_code, opi.warehouse_name,
            opi.soure_no, opi.change_count, opi.item_stat, opi.delivery_date, opi.reply_date,
            opi.confirm_date, opi.purchase_remark, opi.vendor_remark, opi.make_num, opi.order_num,
            opi.purchase_confirm, opi.vendor_confirm, opi.purchase_doc_url, opi.vendor_doc_url,
            opi.receive_num, opi.wait_num, opi.is_main, opi.main_item_id, opi.sale_employee_name,
            opi.goods_class_name,opi.delivery_stat, opi.delete_flag,opi.source_item_id,
            opi.erp_master_num,opi.erp_reject_num
        FROM
          order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id
        <where>
            op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.item_stat = 4
            <if test="params.tenantId != null and params.tenantId != ''">
                AND op.tenant_id = #{params.tenantId}
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                AND op.dept_id = #{params.deptId}
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                AND op.vendor_id = #{params.vendorId}
            </if>
            <if test="params.purNo !=null and params.purNo != ''">
                AND op.pur_no like CONCAT(,#{params.purNo},'%')
            </if>
            <if test="params.goods !=null and params.goods != ''">
                AND (
                opi.goods_erp_code like CONCAT(#{params.goods},'%')
                or opi.goods_name like CONCAT(#{params.goods},'%')
                or opi.goods_model like CONCAT(#{params.goods},'%')
                )
            </if>
        </where>
        ORDER BY op.id DESC
    </select>

    <!-- 获取订单明细的订单数量汇总 -->
    <select id="getOrderItemSumNum" resultType="java.math.BigDecimal">
        SELECT
               SUM(order_item_sum.order_num)
        FROM (
            SELECT
                opi.id,IFNULL(opi.order_num,0) order_num
            FROM
                order_pur op,order_pur_item opi
            WHERE
                op.id = opi.pur_id
                AND op.delete_flag = 0 AND opi.delete_flag = 0
                <!-- 日期区间 -->
                <if test="params.startDate != null and params.endDate != null and params.startDate != '' and params.endDate != ''">
                    AND op.order_date BETWEEN #{params.startDate} AND #{params.endDate}
                </if>
                <!-- 租户(采购方进入) -->
                <if test="params.tenantId != null and params.tenantId != ''">
                    AND op.tenant_id = #{params.tenantId}
                </if>
                <if test="params.deptCode != null and params.deptCode != ''">
                    AND op.dept_code = #{params.deptCode}
                </if>
                <if test="params.goodsId != null and params.goodsId != ''">
                    AND opi.goods_id = #{params.goodsId}
                </if>
        ) order_item_sum
    </select>

    <!--  获取订单校验接口所需数据返回给SAP  -->
    <select id="getOrderCheckDataToSAP" resultType="com.dian.modules.order.vo.OrderCheckVO">
        SELECT
            op.pur_no,opi.seq,op.vendor_code as vendor_erp_code,opi.goods_erp_code,opi.matched_plan_num
        FROM
            order_pur op,order_pur_item opi
        WHERE
            op.id = opi.pur_id
        <if test="params.purNo != null">
            AND opi.soure_no = #{params.purNo}
        </if>
        <if test="params.seq != null">
            AND opi.seq in
            <foreach item="item" index="index" collection='params.seq.split(",")' open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.vendorErpCode != null">
            AND op.vendor_code = #{params.vendorErpCode}
        </if>
        <if test="params.goodsErpCode != null">
            AND opi.goods_erp_code = #{params.goodsErpCode}
        </if>
    </select>

    <select id="queryPurOrderSlaveList" parameterType="com.dian.modules.order.query.PurQuery" resultType="com.dian.modules.order.vo.PurItemLineVO">
        SELECT
            opi.id,opi.pur_id,opi.seq,op.vendor_id,op.vendor_code,op.vendor_name
            ,opi.goods_id,opi.goods_erp_code,opi.goods_code,opi.goods_name,opi.goods_model
            ,opi.uom_id,opi.uom_code,opi.uom_name,opi.rate_id,opi.rate_name,opi.rate_val
            ,opi.currency_id,opi.currency_name,opi.taxes_type,opi.invoice_type,opi.gst_price,opi.soure_no,opi.source_item_id
            ,op.order_type,op.pur_id pur_user_id,op.pur_name pur_user_name,op.vendor_delivery_address,op.shipping_address
            ,op.purchasing_group
        FROM
            order_pur op,order_pur_item opi
        WHERE
            op.id = opi.pur_id
        AND op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.delete_flag = 0
        AND op.stat = 5
        <if test="tenantId != null and tenantId != ''">
            AND opi.tenant_id = #{tenantId}
        </if>
        <if test="itemStat != null and itemStat != ''">
            AND opi.item_stat = #{itemStat}
        </if>
        <if test="deliveryType != null and deliveryType != ''">
            AND opi.delivery_type = #{deliveryType}
        </if>
        <if test="sourceItemId != null and sourceItemId != ''">
            AND opi.source_item_id = #{sourceItemId}
        </if>
        <if test="sourceItemIds != null and sourceItemIds.size() > 0">
            AND opi.source_item_id IN
            <foreach item="item" index="index" collection='sourceItemIds' open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="goodsErpCode != null and goodsErpCode != ''">
            AND opi.goods_erp_code = #{goodsErpCode}
        </if>
        ORDER BY op.id DESC
    </select>
</mapper>
