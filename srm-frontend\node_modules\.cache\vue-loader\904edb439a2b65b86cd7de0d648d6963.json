{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\tenant\\index.vue?vue&type=template&id=8dd9ceb8&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\tenant\\index.vue", "mtime": 1754027991188}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n  <div class=\"DIAN-common-layout\">\n    <div class=\"DIAN-common-layout-center\">\n\n      <!-- 搜索框 -->\n      <el-row class=\"DIAN-common-search-box\" :gutter=\"16\">\n        <el-form @submit.native.prevent>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.purNo\" placeholder=\"请输入订单号\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.vendor\" placeholder=\"请输入供应商编码/名称\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.goods\" placeholder=\"请输入物料编码/名称/规格\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.purName\" placeholder=\"请输入采购员名称\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <template v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item>\n                <el-date-picker\n                  v-model=\"queryParam.orderDate\"\n                  type=\"daterange\"\n                  placeholder=\"请输入订单日期\"\n                  range-separator=\"至\"\n                  start-placeholder=\"（订单）开始日期\"\n                  end-placeholder=\"（订单）结束日期\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"4\">\n              <el-form-item>\n                <el-select v-model=\"queryParam.finishStatus\" placeholder=\"请选择完成状态\" clearable>\n                  <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                            v-for=\"item in finishStatusOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n           <!-- <el-col :span=\"4\">\n              <el-form-item>\n                <el-select v-model=\"queryParam.returnMark\" placeholder=\"是否退货订单\" clearable>\n                  <el-option\n                    :key=\"item.key\"\n                    :label=\"item.value\"\n                    :value=\"parseInt(item.key)\"\n                    v-for=\"item in validStat\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"4\">\n              <el-form-item>\n                <el-select v-model=\"queryParam.freeMark\" placeholder=\"是否免费订单\" clearable>\n                  <el-option\n                    :key=\"item.key\"\n                    :label=\"item.value\"\n                    :value=\"parseInt(item.key)\"\n                    v-for=\"item in validStat\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col> -->\n          </template>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <!-- 查询按钮 -->\n              <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search()\">\n                {{ $t('common.search') }}\n              </el-button>\n              <!-- 重置按钮 -->\n              <el-button icon=\"el-icon-refresh-right\" @click=\"reset()\">\n                {{ $t('common.reset') }}\n              </el-button>\n              <el-button type=\"text\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\"\n                         v-if=\"!showAll\">展开\n              </el-button>\n              <el-button type=\"text\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>\n                收起\n              </el-button>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n\n      <!-- body -->\n      <div class=\"DIAN-common-layout-main DIAN-flex-main\">\n        <!-- 表头工具栏 -->\n        <div class=\"DIAN-common-head\">\n          <div>\n\n            <template>\n              <el-button type=\"primary\" @click=\"exportHandle()\" icon=\"el-icon-top\" v-has-per=\"'order:pur:progressExport'\">{{ $t('common.exportBtn') }}</el-button>\n            </template>\n          </div>\n          <div class=\"DIAN-common-head-right\">\n            <el-tooltip effect=\"dark\" :content=\"$t('common.refresh')\" placement=\"top\">\n              <el-link icon=\"icon-ym icon-ym-Refresh DIAN-common-head-icon\" :underline=\"false\" @click=\"search()\" />\n            </el-tooltip>\n            <d-screen-full/>\n          </div>\n        </div>\n\n        <!-- 表格 -->\n        <d-table ref=\"listTable\" v-loading=\"listLoading\" :data=\"list\" hasC show-summary>\n          <el-table-column prop=\"deptName\" label=\"采购组织\" align=\"center\" show-overflow-tooltip width=\"120\"/>\n          <el-table-column prop=\"purNo\" label=\"订单号/序号\" align=\"center\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.purNo + '/' + scope.row.seq}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"purName\" label=\"采购员\" align=\"center\" show-overflow-tooltip width=\"90\"/>\n          <el-table-column prop=\"orderType\" label=\"订单类型\" align=\"center\" show-overflow-tooltip width=\"100\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.orderType | commonEnumsTurn(\"common.JinDieOrderTypeEnum\")}}</span>\n            </template>\n          </el-table-column>\n          <!-- <el-table-column prop=\"mrpRegion\" label=\"MRP区域\" align=\"center\" show-overflow-tooltip width=\"100\"/> -->\n          <el-table-column prop=\"vendorCode\" label=\"供应商编码\" align=\"center\" width=\"100\"/>\n          <el-table-column prop=\"vendorName\" label=\"供应商名称\" align=\"center\" show-overflow-tooltip width=\"150\"/>\n          <el-table-column prop=\"goodsErpCode\" label=\"物料编码\" align=\"center\" show-overflow-tooltip width=\"110\"/>\n          <el-table-column prop=\"goodsName\" label=\"物料名称\" align=\"center\" show-overflow-tooltip width=\"120\"/>\n          <el-table-column prop=\"goodsModel\" label=\"规格型号\" align=\"center\" show-overflow-tooltip width=\"130\"/>\n          <el-table-column prop=\"deliveryDate\" label=\"交货日期\" align=\"center\" show-overflow-tooltip width=\"100\">\n            <template slot-scope=\"scope\">\n              <span>{{ $dian.dateFormat(scope.row.deliveryDate, 'YYYY-MM-DD') }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"finishStatus\" label=\"完成状态\" align=\"center\" width=\"90\">\n            <template slot-scope=\"scope\">\n              <span>{{ (scope.row.orderSum === (scope.row.erpMasterNum - scope.row.erpRejectNum)) | commonEnumsTurn(\"base.SampleCompleteSignEnum\") }}</span>\n            </template>\n          </el-table-column>\n          <!-- <el-table-column prop=\"itemStat\" label=\"产品状态\" align=\"center\" width=\"90\">\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.itemStat | commonEnumsTurn(\"order.PurlineStatEnum\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"freeMark\" label=\"是否免费\" align=\"center\" width=\"80\">\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.freeMark | commonEnumsTurn(\"comm.ValidEnum\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"returnMark\" label=\"是否退货\" align=\"center\" width=\"80\">\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.returnMark | commonEnumsTurn(\"comm.ValidEnum\") }}</span>\n            </template>\n          </el-table-column> -->\n<!--          <el-table-column prop=\"auxUomName\" label=\"单位\" align=\"center\" show-overflow-tooltip width=\"50\"/>-->\n          <el-table-column prop=\"orderNum\" label=\"订单数量\" align=\"center\" show-overflow-tooltip width=\"80\"/>\n          <!-- <el-table-column prop=\"matchedPlanNum\" label=\"已匹配计划量\" align=\"center\" show-overflow-tooltip width=\"100\"/> -->\n          <!-- <el-table-column prop=\"canMatchPlanNum\" label=\"可匹配计划量\" align=\"center\" show-overflow-tooltip width=\"100\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.returnMark != 1\">{{scope.row.canMatchPlanNum}}</span>\n              <span v-else>0</span>\n            </template>\n          </el-table-column> -->\n          <el-table-column prop=\"makeNum\" label=\"已制单量\" align=\"center\" show-overflow-tooltip width=\"100\">\n          </el-table-column>\n          <el-table-column prop=\"canMakeNum\" label=\"未制单量\" align=\"center\" show-overflow-tooltip width=\"100\">\n          </el-table-column>\n          <el-table-column prop=\"fixNum\" label=\"已送货量\" align=\"center\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column prop=\"waitNum\" label=\"待送货量\" align=\"center\" show-overflow-tooltip width=\"100\"/>\n          <!-- <el-table-column prop=\"unCompetentNum\" label=\"不合格数\" align=\"center\" show-overflow-tooltip width=\"100\"/> -->\n          <el-table-column prop=\"refundNum\" label=\"暂退补料数量\" width=\"100\"/>\n          <el-table-column prop=\"refDedNum\" label=\"暂退扣款数量\" width=\"100\"/>\n          <el-table-column prop=\"erpMasterNum\" label=\"入库数量\" align=\"center\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column prop=\"erpRejectNum\" label=\"退货补料数量\" width=\"100\"/>\n          <el-table-column prop=\"retDedNum\" label=\"退货扣款数量\" width=\"100\"/>\n          <!-- <el-table-column prop=\"shippingAddress\" label=\"交货地址\" align=\"center\" show-overflow-tooltip/> -->\n          <el-table-column prop=\"gstPrice\" label=\"含税单价\" align=\"center\" show-overflow-tooltip width=\"100\" v-if=\"$dian.hasPerBtnP('order:pur:lookPrice')\"/>\n          <el-table-column prop=\"gstAmount\" label=\"含税金额\" align=\"center\" show-overflow-tooltip width=\"100\" v-if=\"$dian.hasPerBtnP('order:pur:lookPrice')\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.gstPrice * scope.row.orderNum}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"remark\" label=\"备注\" align=\"center\" show-overflow-tooltip/>\n          <el-table-column label=\"操作\" width=\"80\" fixed=\"right\" align=\"center\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" size=\"mini\" @click=\"addEditOrderHandle(scope.row.id)\" v-has-per=\"'order:pur:info'\"> {{ $t('common.lookBtn')}} </el-button>\n            </template>\n          </el-table-column>\n        </d-table>\n        <d-pagination :total=\"total\" :page.sync=\"queryParam.page\" :limit.sync=\"queryParam.limit\" @pagination=\"initData\"/>\n      </div>\n\n      <!-- FORM表单 -->\n      <Form ref=\"form\" v-show=\"formVisible\" @callRefreshList=\"closeForm\"></Form>\n      <!-- 下载/导出 组件 -->\n      <d-export ref=\"export\" title=\"订单执行进度表导出\" :exports=\"downExports\"></d-export>\n      <!-- 导入模板 -->\n      <d-import ref=\"upload\" @callData=\"search()\" />\n\n\n    </div>\n  </div>\n", null]}