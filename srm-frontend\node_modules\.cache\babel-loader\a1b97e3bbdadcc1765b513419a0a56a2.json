{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\progress\\tenant\\index.vue", "mtime": 1754019228380}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _dianUiVue = require(\"@dian/dian-ui-vue\");\nvar _Form = _interopRequireDefault(require(\"@/views/order/produce/tenant/Form\"));\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _sale = require(\"@/api/order/sale\");\nvar _dian = _interopRequireDefault(require(\"@/utils/dian\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  //加载底层公有组件\n  mixins: [_dianUiVue.dFlowMixin],\n  name: \"order-progress-tenant\",\n  components: {\n    Form: _Form.default\n  },\n  data: function data() {\n    return {\n      bsartTypeOptions: _store.default.getters.commonEnums['order.BsartTypeEnum'],\n      // 订单类型\n      orderLineStat: _store.default.getters.commonEnums['order.PurlineStatEnum'],\n      // 订单明细单据状态\n      validStat: _store.default.getters.commonEnums['comm.ValidEnum'],\n      // 订单明细单据状态\n      selectTypeOptions: [\n      // 查询类型（完成状态）\n      {\n        key: '1',\n        value: '未完成'\n      }, {\n        key: '2',\n        value: '已完成'\n      }, {\n        key: '3',\n        value: '全部'\n      }],\n      queryParam: {\n        page: 1,\n        limit: 20,\n        orderNo: '',\n        // 订单号\n        vendor: '',\n        // 供应商编码|名称\n        goods: '',\n        // 物料编码|名称|描述|图号\n        orderDate: '',\n        // 订单日期\n        startDate: '',\n        // 订单日期 - 开始日期\n        endDate: '',\n        // 订单日期 - 结束日期\n        orderType: '',\n        // 订单类型\n        itemStat: '',\n        // 单据状态\n        deptId: '',\n        //组织机构id\n        isTenant: 'ture',\n        // 是否是采购方进入\n        isDESC: 'ture',\n        // 是否倒叙排序\n        purName: '',\n        //采购员名称\n        returnMark: null,\n        // 是否退货\n        freeMark: null,\n        // 是否免费\n        selectType: '3' // 默认查询显示全部的数据\n      },\n\n      showAll: false,\n      formVisible: false,\n      listLoading: false,\n      btnLoading: false,\n      downExports: false,\n      // true-下载模板，false-导出\n      list: [],\n      total: 0\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  watch: {\n    $route: {\n      handler: function handler(to, from) {},\n      immediate: true\n    }\n  },\n  methods: {\n    initData: function initData() {\n      var _this = this;\n      this.listLoading = true;\n      if (this.queryParam.orderDate.length !== 0) {\n        var startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');\n        var endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');\n        this.queryParam.startDate = startDate;\n        this.queryParam.endDate = endDate;\n      } else {\n        this.queryParam.startDate = '';\n        this.queryParam.endDate = '';\n      }\n      var subCompanyInfoData = _dian.default.storageGet('subCompanyInfo');\n      if (subCompanyInfoData) {\n        this.queryParam.deptId = subCompanyInfoData.id;\n      }\n      var date = this.queryParam.orderDate;\n      this.queryParam.orderDate = '';\n      (0, _sale.getTenantSaleOrderProgressList)(this.queryParam).then(function (res) {\n        _this.total = res.data.totalCount;\n        _this.list = res.data.list;\n        _this.queryParam.orderDate = date;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.queryParam.orderDate = date;\n        _this.listLoading = false;\n      });\n    },\n    // 新增|编辑 项目报备\n    addEditOrderHandle: function addEditOrderHandle(id) {\n      var _this2 = this;\n      this.formVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.form.init(id);\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n      //获取所有选中项数组的长度\n      this.selectedNum = selection.length;\n    },\n    // 导出\n    exportHandle: function exportHandle() {\n      this.downExports = true;\n      this.$refs.export.init('/api/order/pur/progressExport', '订单执行进度表', this.queryParam);\n    },\n    // 搜索方法，并返回到第一页\n    search: function search() {\n      this.queryParam.page = 1;\n      this.initData();\n    },\n    // 重置方法\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.search();\n    },\n    // Form表单关闭时回调方法\n    closeForm: function closeForm() {\n      this.formVisible = false;\n      this.initData();\n    }\n  }\n};\nexports.default = _default;", null]}